import BaseService from "./BaseService";
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from "@/utils/errorHandler";
import { LoginCredentials, User } from "@/models/User";
import { UserSignupDTO } from "@/models/DTO/UserDTO";

class AuthService extends BaseService {
  constructor() {
    // Use /auth prefix and configure for auth service
    super("/auth", {
      baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:8082/api",
      enableAuth: false, // Auth service doesn't need auth headers for login/signup
    });
  }

  async login(credentials: LoginCredentials): Promise<User> {
    try {
      const response = await this.post<User>("/login", credentials);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Login");
    }
  }

  async signup(userData: UserSignupDTO): Promise<User> {
    try {
      const response = await this.post<User>("/register", userData);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Signup");
    }
  }

  async forgotPassword(email: string): Promise<{ message: string }> {
    try {
      const response = await this.post<{ message: string }>("/forgot-password", { email });
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Forgot password");
    }
  }
}

const authService = new AuthService();

export default authService;
