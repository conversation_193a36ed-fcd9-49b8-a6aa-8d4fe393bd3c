import BaseService from "./BaseService";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/utils/errorHandler";
import { LoginCredentials } from "@/models/User";
import { UserSignupDTO } from "@/models/DTO/UserDTO";
import { AuthResponse, RegisterRequest, ApiUser, UpdateUserRequest } from "@/types/api";

class AuthService extends BaseService {
  constructor() {
    super("/api/auth", {
      enableAuth: false, // Auth service doesn't need auth headers
    });
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await this.post<AuthResponse>("/login", credentials);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Login");
    }
  }

  async signup(userData: UserSignupDTO): Promise<{ message: string; userId: string }> {
    try {
      const registerRequest: RegisterRequest = {
        email: userData.email,
        username: userData.username,
        password: userData.password,
        role: userData.role === 1 ? "User" : userData.role === 2 ? "Organizer" : "Admin",
      };
      const response = await this.post<{ message: string; userId: string }>(
        "/register",
        registerRequest
      );
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Signup");
    }
  }

  async forgotPassword(email: string): Promise<{ message: string }> {
    try {
      const response = await this.post<{ message: string }>("/forgot-password", { email });
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Forgot password");
    }
  }
}

class UserService extends BaseService {
  constructor() {
    super("/api/user", {
      enableAuth: true,
    });
  }

  async getAllUsers(): Promise<ApiUser[]> {
    try {
      const response = await this.get<ApiUser[]>("");
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Fetch all users");
    }
  }

  async getUserById(userId: string): Promise<ApiUser> {
    try {
      const response = await this.get<ApiUser>(`/${userId}`);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Fetch user by ID (${userId})`);
    }
  }

  async updateUser(userId: string, userData: UpdateUserRequest): Promise<ApiUser> {
    try {
      const response = await this.patch<ApiUser>(`/${userId}`, userData);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Update user (${userId})`);
    }
  }

  async deleteUser(userId: string): Promise<void> {
    try {
      await this.delete(`/${userId}`);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Delete user (${userId})`);
    }
  }
}

const authService = new AuthService();
const userService = new UserService();

export default authService;
export { userService };
