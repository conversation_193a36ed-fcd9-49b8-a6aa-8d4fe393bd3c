(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var r=t(5155);t(2115);var a=t(4624),i=t(2085),l=t(6707);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-darkText shadow-xs hover:bg-primary/70",outline:"border bg-transparent border-darkStroke shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:i,asChild:d=!1,...c}=e,o=d?a.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,l.cn)(n({variant:t,size:i,className:s})),...c})}},1469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return d},getImageProps:function(){return n}});let r=t(8229),a=t(8883),i=t(3063),l=r._(t(1193));function n(e){let{props:s}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let d=i.Image},1492:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},3156:(e,s,t)=>{Promise.resolve().then(t.bind(t,6612))},5196:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6612:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(5155),a=t(2115),i=t(6766);let l=()=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"15",viewBox:"0 0 14 15",fill:"none",children:[(0,r.jsxs)("g",{clipPath:"url(#clip0_72_279)",children:[(0,r.jsx)("path",{d:"M7 13.8638C10.5899 13.8638 13.5 10.9537 13.5 7.36377C13.5 3.77392 10.5899 0.86377 7 0.86377C3.41015 0.86377 0.5 3.77392 0.5 7.36377C0.5 10.9537 3.41015 13.8638 7 13.8638Z",stroke:"#686868",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M7.5 10.8638L9.5 4.86377L3.5 6.86377L6 8.36377L7.5 10.8638Z",stroke:"#686868",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip0_72_279",children:(0,r.jsx)("rect",{width:"14",height:"14",fill:"white",transform:"translate(0 0.36377)"})})})]}),n=()=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"15",viewBox:"0 0 14 15",fill:"none",children:[(0,r.jsxs)("g",{clipPath:"url(#clip0_72_290)",children:[(0,r.jsx)("path",{d:"M7 14.2881C10.0376 14.2881 12.5 11.8257 12.5 8.78809C12.5 5.75052 10.0376 3.28809 7 3.28809C3.96243 3.28809 1.5 5.75052 1.5 8.78809C1.5 11.8257 3.96243 14.2881 7 14.2881Z",stroke:"#686868",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M0.5 3.28809C1.20228 2.47234 2.04999 1.79417 3 1.28809",stroke:"#686868",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M13.5 3.28809C12.7977 2.47234 11.95 1.79417 11 1.28809",stroke:"#686868",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M7 5.78809V8.78809H9.5",stroke:"#686868",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip0_72_290",children:(0,r.jsx)("rect",{width:"14",height:"14",fill:"white",transform:"translate(0 0.788086)"})})})]});function d(){let{eventName:e="FC Barcelona vs Real Madrid",section:s="214",row:t="C",seats:a="4,5,6",location:d="America First Field, Sandy, Utah, USA",date:c="Mar 22 • Sat • 7:30PM • 2025",price:o=20,status:x="Incoming"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.jsxs)("div",{className:"w-full min-w-[400px] h-[104px] flex items-center justify-between rounded-lg border border-darkStroke",children:[(0,r.jsx)(i.default,{width:140,height:104,src:"https://s3-alpha-sig.figma.com/img/5e1d/cca4/1d465015292bd6a0807ef9dd78185596?Expires=1745798400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=AkC17HOXjOVt9K7k~v5jlmEsdSYkUobM946rVYtxFRFEqJH5Jxx9Adt-rti~K9TWlEnaMuGQNB-Fxlz2-rr~PtUfP5Jinp3nJLgpBlqylxsy75xdmrnod5Zm5LYlZf9FsEJGtyJDZIbyvjJpeNzVJwLc8aPKPghqb-TgTQkDuywR10rOcd4kEdKTT19NJrFI4hzJtKwLyTNoBxQenc0VU2LaFcSK8f2sAn3pGQaLzIjRC1cIrdEtQlfLFWwB88fuojghLClR234gALI3GYLMUVWkuJDew62JT5FSuLZGbqkmcpmexmuaxUmHohcj3e4KcAYN7AUHmo66PhzJ2nt5QQ__",alt:"Event",className:"rounded-sm h-full"}),(0,r.jsxs)("div",{className:"px-2 py-1 flex justify-between w-full h-full",children:[(0,r.jsxs)("div",{className:"h-full flex flex-col justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-darkText text-lg",children:e}),(0,r.jsxs)("p",{className:"font-medium text-darkText text-base -mt-1",children:["Section ",s," • Row ",t," • Seats ",a]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,r.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,r.jsx)(l,{}),(0,r.jsx)("p",{className:"text-xs text-grayText",children:d})]}),(0,r.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,r.jsx)(n,{}),(0,r.jsx)("p",{className:"text-xs text-grayText",children:c})]})]})]}),(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"flex flex-col h-[45px] justify-between items-end",children:[(0,r.jsxs)("p",{className:"font-semibold text-base text-darkText text-end",children:["$",o]}),(0,r.jsx)("p",{className:"font-semibold text-sm px-2 py-0.5 text-center ".concat("Incoming"===x?"text-green-500":"Cancelled"===x?"text-red-500":"text-gray-500"),children:x})]})})]})]})}var c=t(7924);let o=(0,t(9946).A)("sliders-horizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var x=t(5196),m=t(1492),u=t(285);let p=Array(10).fill(0).map((e,s)=>({id:s+1,eventName:"FC Barcelona vs Real Madrid",section:"214",row:"C",seats:"4,5,6",location:"America First Field, Sandy, Utah, USA",date:"Mar 22 • Sat • 7:30PM • 2025",price:20,status:s%3==0?"Incoming":s%3==1?"Cancelled":"Passed"}));function h(){let[e,s]=(0,a.useState)(""),[t,i]=(0,a.useState)("All"),[l,n]=(0,a.useState)(null),[h,f]=(0,a.useState)("asc"),[g,j]=(0,a.useState)(p),[y,v]=(0,a.useState)(!1),[w,b]=(0,a.useState)(!1),N=(0,a.useRef)(null),k=(0,a.useRef)(null),C=e=>{i(e),v(!1)},A=(e,s)=>{n(e),f(s),b(!1)};return(0,a.useEffect)(()=>{let e=e=>{N.current&&!N.current.contains(e.target)&&k.current&&!k.current.contains(e.target)&&(v(!1),b(!1))};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.useEffect)(()=>{let s=[...p];if(e){let t=e.toLowerCase();s=s.filter(e=>e.eventName.toLowerCase().includes(t)||e.location.toLowerCase().includes(t))}"All"!==t&&(s=s.filter(e=>e.status===t)),l&&s.sort((e,s)=>"price"===l?"asc"===h?e.price-s.price:s.price-e.price:"date"===l?"asc"===h?e.date.localeCompare(s.date):s.date.localeCompare(e.date):0),j(s)},[e,t,l,h]),(0,r.jsx)("div",{className:"w-full py-4 px-2",children:(0,r.jsxs)("div",{className:"mx-auto w-full max-w-[620px] flex flex-col items-center gap-8",children:[(0,r.jsxs)("div",{className:"w-full flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"relative flex-1",children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search tickets",value:e,onChange:e=>{s(e.target.value)},className:"w-full h-10 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"}),(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500",size:18})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{ref:N,className:"relative",children:[(0,r.jsxs)(u.$,{variant:"outline",size:"sm",className:"flex items-center gap-1 h-10 border-gray-300",onClick:()=>{v(!y),b(!1)},children:[(0,r.jsx)(o,{size:16}),"Filter","All"!==t&&(0,r.jsx)("span",{className:"ml-1 text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full",children:t})]}),y&&(0,r.jsxs)("div",{className:"absolute right-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-10",children:[(0,r.jsx)("div",{className:"p-2 border-b border-gray-200",children:(0,r.jsx)("p",{className:"text-sm font-medium",children:"Filter by Status"})}),(0,r.jsxs)("div",{className:"p-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 hover:bg-gray-100 cursor-pointer rounded-md",onClick:()=>C("All"),children:[(0,r.jsx)("span",{className:"text-sm",children:"All"}),"All"===t&&(0,r.jsx)(x.A,{size:16,className:"text-primary"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 hover:bg-gray-100 cursor-pointer rounded-md",onClick:()=>C("Incoming"),children:[(0,r.jsx)("span",{className:"text-sm",children:"Incoming"}),"Incoming"===t&&(0,r.jsx)(x.A,{size:16,className:"text-primary"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 hover:bg-gray-100 cursor-pointer rounded-md",onClick:()=>C("Passed"),children:[(0,r.jsx)("span",{className:"text-sm",children:"Passed"}),"Passed"===t&&(0,r.jsx)(x.A,{size:16,className:"text-primary"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 hover:bg-gray-100 cursor-pointer rounded-md",onClick:()=>C("Cancelled"),children:[(0,r.jsx)("span",{className:"text-sm",children:"Cancelled"}),"Cancelled"===t&&(0,r.jsx)(x.A,{size:16,className:"text-primary"})]})]})]})]}),(0,r.jsxs)("div",{ref:k,className:"relative",children:[(0,r.jsxs)(u.$,{variant:"outline",size:"sm",className:"flex items-center gap-1 h-10 border-gray-300",onClick:()=>{b(!w),v(!1)},children:[(0,r.jsx)(m.A,{size:16}),"Sort by price",l&&(0,r.jsx)("span",{className:"ml-1 text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full",children:"asc"===h?"↑":"↓"})]}),w&&(0,r.jsxs)("div",{className:"absolute right-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-10",children:[(0,r.jsx)("div",{className:"p-2 border-b border-gray-200",children:(0,r.jsx)("p",{className:"text-sm font-medium",children:"Sort by"})}),(0,r.jsxs)("div",{className:"p-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 hover:bg-gray-100 cursor-pointer rounded-md",onClick:()=>A(null,"asc"),children:[(0,r.jsx)("span",{className:"text-sm",children:"No sorting"}),null===l&&(0,r.jsx)(x.A,{size:16,className:"text-primary"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 hover:bg-gray-100 cursor-pointer rounded-md",onClick:()=>A("price","asc"),children:[(0,r.jsx)("span",{className:"text-sm",children:"Price (Low to High)"}),"price"===l&&"asc"===h&&(0,r.jsx)(x.A,{size:16,className:"text-primary"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 hover:bg-gray-100 cursor-pointer rounded-md",onClick:()=>A("price","desc"),children:[(0,r.jsx)("span",{className:"text-sm",children:"Price (High to Low)"}),"price"===l&&"desc"===h&&(0,r.jsx)(x.A,{size:16,className:"text-primary"})]})]})]})]})]})]}),(0,r.jsx)("div",{className:"w-full flex flex-col gap-4",children:g.length>0?g.map(e=>(0,r.jsx)(d,{id:e.id,eventName:e.eventName,section:e.section,row:e.row,seats:e.seats,location:e.location,date:e.date,price:e.price,status:e.status},e.id)):(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No orders found matching your criteria"})})]})})}},6707:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(2596),a=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}},6766:(e,s,t)=>{"use strict";t.d(s,{default:()=>a.a});var r=t(1469),a=t.n(r)},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),l=e=>{let s=i(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:c="",children:o,iconNode:x,...m}=e;return(0,r.createElement)("svg",{ref:s,...d,width:a,height:a,stroke:t,strokeWidth:l?24*Number(i)/Number(a):i,className:n("lucide",c),...m},[...x.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(o)?o:[o]])}),o=(e,s)=>{let t=(0,r.forwardRef)((t,i)=>{let{className:d,...o}=t;return(0,r.createElement)(c,{ref:i,iconNode:s,className:n("lucide-".concat(a(l(e))),"lucide-".concat(e),d),...o})});return t.displayName=l(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[13,63,441,684,358],()=>s(3156)),_N_E=e.O()}]);