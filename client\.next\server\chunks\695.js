exports.id=695,exports.ids=[695],exports.modules={3377:(e,t,a)=>{"use strict";a.d(t,{i$:()=>g,TL:()=>v,HB:()=>I,b9:()=>w,lh:()=>u,Ay:()=>C,SX:()=>p,fw:()=>c,vR:()=>l,ls:()=>m,nK:()=>y,P_:()=>A,qM:()=>h});var r=a(9317),n=a(59590),s=a(86835),i=a(98680);class o extends n.A{constructor(){super("/api/events",{enableAuth:!0}),this.updateEvent=async(e,t)=>{try{let a={title:t.name,description:t.description},r=await this.patch(`/${e}`,a);return(0,i.en)(r.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Update event (${e})`)}}}parseEventDates(e){return{...e,createdAt:e.createdAt?new Date(e.createdAt):new Date,updatedAt:e.updatedAt?new Date(e.updatedAt):new Date,startDateTime:new Date(e.startDateTime),endDateTime:new Date(e.endDateTime)}}async getAllEvents(){try{return(await this.get("")).data.map(e=>(0,i.en)(e))}catch{return console.warn("Failed to fetch events from API, using sample data"),[]}}async getPublishedEvents(){try{return(await this.get("/published")).data.map(e=>(0,i.en)(e))}catch(e){s.zc.handleServiceErrorFromCatch(e,"Fetch published events")}}async createEvent(e){try{let t=(0,i.Zm)(e),a=await this.post("",t);return(0,i.en)(a.data)}catch(e){s.zc.handleServiceErrorFromCatch(e,"Create event")}}async getEventById(e){try{let t=await this.get(`/${e}`);if(t.data)return(0,i.en)(t.data);return null}catch(n){console.warn(`Failed to fetch event ${e} from API, trying sample data`);let{sampleEvents:t}=await a.e(244).then(a.bind(a,84244)),r=t.find(t=>t.eventId===e);if(r)return r;s.zc.handleServiceErrorFromCatch(n,`Fetch event by ID (${e})`)}}async rescheduleEvent(e,t){try{let a={date:new Date(t.newStartDateTime).toISOString().split("T")[0],startTime:new Date(t.newStartDateTime).toTimeString().slice(0,5),endTime:new Date(t.newEndDateTime).toTimeString().slice(0,5)},r=await this.patch(`/${e}/reschedule`,a);return(0,i.en)(r.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Reschedule event (${e})`)}}async deleteEvent(e){try{await this.delete(`/${e}`)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Delete event (${e})`)}}async cancelEvent(e){try{let t=await this.patch(`/${e}/cancel`);return(0,i.en)(t.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Cancel event (${e})`)}}async approveEvent(e){try{let t=await this.patch(`/${e}/approve`);return(0,i.en)(t.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Approve event (${e})`)}}async postponeEvent(e){try{let t=await this.patch(`/${e}/postpone`);return(0,i.en)(t.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Postpone event (${e})`)}}async submitEvent(e){try{let t=await this.patch(`/${e}/submit-for-approval`);return(0,i.en)(t.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Submit event (${e})`)}}}let d=new o,c=(0,r.zD)("events/fetchAll",async(e,{rejectWithValue:t})=>{try{return await d.getAllEvents()}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),l=(0,r.zD)("events/fetchById",async(e,{rejectWithValue:t})=>{try{return await d.getEventById(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,r.zD)("events/addNew",async(e,{rejectWithValue:t})=>{try{return await d.createEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),h=(0,r.zD)("events/updateEvent",async({eventId:e,eventData:t},{rejectWithValue:a})=>{try{return await d.updateEvent(e,t)}catch(e){return a(s.zc.handleAsyncThunkErrorFromCatch(e))}}),p=(0,r.zD)("events/remove",async(e,{rejectWithValue:t})=>{try{return await d.deleteEvent(e),e}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),y=(0,r.zD)("events/reschedule",async({eventId:e,rescheduleData:t},{rejectWithValue:a})=>{try{return await d.rescheduleEvent(e,t)}catch(e){return a(s.zc.handleAsyncThunkErrorFromCatch(e))}}),m=(0,r.zD)("events/postpone",async(e,{rejectWithValue:t})=>{try{return await d.postponeEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),v=(0,r.zD)("events/cancel",async(e,{rejectWithValue:t})=>{try{return await d.cancelEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),g=(0,r.zD)("events/approve",async(e,{rejectWithValue:t})=>{try{return await d.approveEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),A=(0,r.zD)("events/submit",async(e,{rejectWithValue:t})=>{try{return await d.submitEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),f=(0,r.Z0)({name:"events",initialState:{events:[],currentEvent:null,isLoadingList:!1,isLoadingDetails:!1,isLoadingMutation:!1,errorList:null,errorDetails:null,errorMutation:null},reducers:{clearCurrentEvent:e=>{e.currentEvent=null,e.errorDetails=null},clearMutationError:e=>{e.errorMutation=null}},extraReducers:e=>{e.addCase(c.pending,e=>{e.isLoadingList=!0,e.errorList=null}).addCase(c.fulfilled,(e,t)=>{e.isLoadingList=!1,e.events=t.payload}).addCase(c.rejected,(e,t)=>{e.isLoadingList=!1,e.errorList=t.payload}).addCase(l.pending,e=>{e.isLoadingDetails=!0,e.errorDetails=null,e.currentEvent=null}).addCase(l.fulfilled,(e,t)=>{e.isLoadingDetails=!1,e.currentEvent=t.payload}).addCase(l.rejected,(e,t)=>{e.isLoadingDetails=!1,e.errorDetails=t.payload}).addCase(u.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(u.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.events.push(t.payload)}).addCase(u.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(h.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(h.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let a=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==a&&(e.events[a]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(h.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(p.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(p.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.events=e.events.filter(e=>e.eventId!==t.payload),e.currentEvent?.eventId===t.payload&&(e.currentEvent=null)}).addCase(p.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(y.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(y.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let a=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==a&&(e.events[a]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(y.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(m.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(m.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let a=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==a&&(e.events[a]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(m.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(v.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(v.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let a=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==a&&(e.events[a]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(v.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(g.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(g.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let a=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==a&&(e.events[a]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(g.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(A.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(A.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let a=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==a&&(e.events[a]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(A.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload})}}),{clearCurrentEvent:I,clearMutationError:w}=f.actions,C=f.reducer},4384:(e,t,a)=>{"use strict";a.d(t,{default:()=>A});var r=a(60687),n=a(9317),s=a(8866),i=a(3377),o=a(80991);let d=(0,n.Z0)({name:"booking",initialState:{selectedSeats:[],eventId:null,totalPrice:0},reducers:{setEventId:(e,t)=>{e.eventId=t.payload},addSeat:(e,t)=>{let{seat:a,price:r}=t.payload;e.selectedSeats.find(e=>e.seatId===a.seatId)||(e.selectedSeats.push(a),e.totalPrice+=r)},removeSeat:(e,t)=>{let{seatId:a,price:r}=t.payload;e.selectedSeats=e.selectedSeats.filter(e=>e.seatId!==a),e.totalPrice-=r},toggleSeat:(e,t)=>{let{seat:a,price:r}=t.payload,n=e.selectedSeats.findIndex(e=>e.seatId===a.seatId);-1!==n?(e.selectedSeats.splice(n,1),e.totalPrice-=r):(e.selectedSeats.push(a),e.totalPrice+=r)},clearSelectedSeats:e=>{e.selectedSeats=[],e.totalPrice=0},clearBooking:e=>{e.selectedSeats=[],e.eventId=null,e.totalPrice=0}}}),{setEventId:c,addSeat:l,removeSeat:u,toggleSeat:h,clearSelectedSeats:p,clearBooking:y}=d.actions,m=d.reducer,v=(0,n.U1)({reducer:{user:s.Ay,events:i.Ay,venues:o.Ay,booking:m},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"],ignoredActionsPaths:["meta.arg","payload.timestamp"],ignoredPaths:["items.dates"],isSerializable:e=>e instanceof Date||"object"!=typeof e||null===e||Array.isArray(e)||"[object Object]"===Object.prototype.toString.call(e)}})});a(43210);var g=a(54864);function A({children:e}){return(0,r.jsx)(g.Kq,{store:v,children:e})}},8866:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>v,Lx:()=>l,w2:()=>h,Ly:()=>m,E_:()=>u});var r=a(59590),n=a(86835);class s extends r.A{constructor(){super("/api/auth",{enableAuth:!1})}async login(e){try{let t={email:e.email,password:e.password};return(await this.post("/login",t)).data}catch(e){n.zc.handleServiceErrorFromCatch(e,"Login")}}async signup(e){try{let t={email:e.email,username:e.username,password:e.password,role:1===e.role?"User":2===e.role?"Organizer":"Admin"};return(await this.post("/register",t)).data}catch(e){n.zc.handleServiceErrorFromCatch(e,"Signup")}}async forgotPassword(e){try{return(await this.post("/forgot-password",{email:e})).data}catch(e){n.zc.handleServiceErrorFromCatch(e,"Forgot password")}}}class i extends r.A{constructor(){super("/api/user",{enableAuth:!0})}async getAllUsers(){try{return(await this.get("")).data}catch(e){n.zc.handleServiceErrorFromCatch(e,"Fetch all users")}}async getUserById(e){try{return(await this.get(`/${e}`)).data}catch(t){n.zc.handleServiceErrorFromCatch(t,`Fetch user by ID (${e})`)}}async updateUser(e,t){try{return(await this.patch(`/${e}`,t)).data}catch(t){n.zc.handleServiceErrorFromCatch(t,`Update user (${e})`)}}async deleteUser(e){try{await this.delete(`/${e}`)}catch(t){n.zc.handleServiceErrorFromCatch(t,`Delete user (${e})`)}}}let o=new s;new i;var d=a(9317),c=a(98680);let l=(0,d.zD)("user/login",async(e,{rejectWithValue:t})=>{try{let t=await o.login(e);return(0,c.cR)(t),(0,c.mO)(t)}catch(e){return t(n.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,d.zD)("auth/signup",async(e,{rejectWithValue:t})=>{try{let t=await o.signup(e);return{message:t.message,userId:t.userId}}catch(e){return t(n.zc.handleAsyncThunkErrorFromCatch(e))}}),h=(0,d.zD)("auth/forgotPassword",async(e,{rejectWithValue:t})=>{try{return await o.forgotPassword(e)}catch(e){return t(n.zc.handleAsyncThunkErrorFromCatch(e))}}),p=(0,d.Z0)({name:"user",initialState:{user:null,isLoading:!1,error:null,isAuthenticated:!1,forgotPasswordStatus:"idle",forgotPasswordError:null},reducers:{logout:e=>{e.user=null,e.isAuthenticated=!1,e.error=null},resetForgotPasswordState:e=>{e.forgotPasswordStatus="idle",e.forgotPasswordError=null}},extraReducers:e=>{e.addCase(l.pending,e=>{e.isLoading=!0,e.error=null}).addCase(l.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload,e.isAuthenticated=!0,e.error=null}).addCase(l.rejected,(e,t)=>{e.isLoading=!1,e.user=null,e.isAuthenticated=!1,e.error=t.payload||"Login failed"}).addCase(u.pending,e=>{e.isLoading=!0,e.error=null}).addCase(u.fulfilled,e=>{e.isLoading=!1,e.error=null}).addCase(u.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload||"Signup failed"}).addCase(h.pending,e=>{e.forgotPasswordStatus="loading",e.forgotPasswordError=null}).addCase(h.fulfilled,e=>{e.forgotPasswordStatus="succeeded",e.forgotPasswordError=null}).addCase(h.rejected,(e,t)=>{e.forgotPasswordStatus="failed",e.forgotPasswordError=t.payload||"Failed to send reset link"})}}),{logout:y,resetForgotPasswordState:m}=p.actions,v=p.reducer},19081:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,4536,23)),Promise.resolve().then(a.t.bind(a,49603,23)),Promise.resolve().then(a.bind(a,38917)),Promise.resolve().then(a.bind(a,82922))},32837:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r={src:"/_next/static/media/logo_app.9e88789c.png",height:39,width:228,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAABlBMVEU9PT0+Pj7oIj+7AAAAAnRSTlMteuZpD/UAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAMSURBVHicY2BkgAAAABEAAvIZoJkAAAAASUVORK5CYII=",blurWidth:8,blurHeight:1}},38917:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r={src:"/_next/static/media/logo_app.9e88789c.png",height:39,width:228,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAABlBMVEU9PT0+Pj7oIj+7AAAAAnRSTlMteuZpD/UAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAMSURBVHicY2BkgAAAABEAAvIZoJkAAAAASUVORK5CYII=",blurWidth:8,blurHeight:1}},53039:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p,metadata:()=>h});var r=a(37413);a(61135);var n=a(27944);let s=a.n(n)().className;a(61120);var i=a(38917),o=a(53384),d=a(4536),c=a.n(d);function l(){return(0,r.jsxs)("div",{className:"w-full h-[70px] flex flex-row justify-between items-center px-8 border-b-1 border-b-gray-200",children:[(0,r.jsx)("div",{className:"logo-img w-[11%]",children:(0,r.jsx)(c(),{href:"/",children:(0,r.jsx)(o.default,{src:i.default,alt:"Logo",className:"w-full h-full block cursor-pointer"})})}),(0,r.jsxs)("div",{className:"flex flex-row items-center gap-6",children:[(0,r.jsx)(c(),{href:"/",className:"text-xl font-medium text-[16px] hover:bg-gray-100 rounded-xl p-2",children:"Home"}),(0,r.jsx)(c(),{href:"/",className:"text-xl font-medium text-[16px] hover:bg-gray-100 rounded-xl p-2",children:"My orders"})]})]})}var u=a(82922);let h={title:"Ticket Booking",description:"Where you can buy tickets",icons:{icon:"/logo.svg"}};function p({children:e}){return(0,r.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsx)(u.default,{children:(0,r.jsxs)("body",{className:"w-full min-h-screen",children:[(0,r.jsx)(l,{}),(0,r.jsx)("main",{className:`${s}`,children:e})]})})})}},59590:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(51060),n=a(15659),s=a(86835);class i{constructor(e,t){this.isRefreshing=!1,this.failedQueue=[],this.prefixUrl=e||"";let a={...{baseURL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8080",timeout:1e4,retryAttempts:3,retryDelay:1e3,enableAuth:!0},...t};this.retryConfig={attempts:a.retryAttempts,delay:a.retryDelay,backoffFactor:2},this.axiosInstance=r.A.create({baseURL:`${a.baseURL}${this.prefixUrl}`,timeout:a.timeout,headers:{"Content-Type":"application/json"}}),a.enableAuth&&this.setupInterceptors()}setupInterceptors(){this.axiosInstance.interceptors.request.use(e=>{let t=n.A.get("token");return t&&(e.headers.Authorization=`Bearer ${t}`,console.log("Token added to headers")),e},e=>Promise.reject(e)),this.axiosInstance.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(()=>this.axiosInstance(t)).catch(e=>Promise.reject(e));t._retry=!0,this.isRefreshing=!0;try{let e=n.A.get("refreshToken");if(!e)throw Error("No refresh token available");let{accessToken:a}=(await this.axiosInstance.post("/account/refresh-token",{refreshToken:e})).data.data;return n.A.set("token",a),this.axiosInstance.defaults.headers.common.Authorization=`Bearer ${a}`,t.headers.Authorization=`Bearer ${a}`,this.processQueue(null),this.axiosInstance(t)}catch(e){return this.processQueue(e),n.A.remove("token"),n.A.remove("refreshToken"),window.location.href="/login",Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}processQueue(e){this.failedQueue.forEach(t=>{e?t.reject(e):t.resolve()}),this.failedQueue=[]}async delay(e){return new Promise(t=>setTimeout(t,e))}async retryRequest(e,t=1){try{return await e()}catch(r){if(t>=this.retryConfig.attempts||!(!r.response||r.response.status>=500&&r.response.status<600))throw s.zc.fromCatch(r);let a=this.retryConfig.delay*Math.pow(this.retryConfig.backoffFactor,t-1);return await this.delay(a),this.retryRequest(e,t+1)}}async get(e,t){return this.retryRequest(()=>this.axiosInstance.get(e,t))}async post(e,t,a){return this.retryRequest(()=>this.axiosInstance.post(e,t,a))}async put(e,t,a){return this.retryRequest(()=>this.axiosInstance.put(e,t,a))}async patch(e,t,a){return this.retryRequest(()=>this.axiosInstance.patch(e,t,a))}async delete(e,t){return this.retryRequest(()=>this.axiosInstance.delete(e,t))}}let o=i},61135:()=>{},80991:(e,t,a)=>{"use strict";a.d(t,{Mo:()=>f,Ay:()=>w,Of:()=>c,rh:()=>y,qT:()=>l});var r=a(9317),n=a(59590),s=a(86835),i=a(98680);class o extends n.A{constructor(){super("/api/venues",{enableAuth:!0})}async getAllVenues(){try{return(await this.get("")).data.map(e=>(0,i.u4)(e))}catch(e){s.zc.handleServiceErrorFromCatch(e,"Fetch venues")}}async getVenueById(e){try{let t=await this.get(`/${e}`);return(0,i.u4)(t.data)}catch(e){s.zc.handleServiceErrorFromCatch(e,"Fetch venue by ID")}}async createVenue(e){try{let t={name:e.name,address:e.address,city:e.city,ownerUserId:e.ownerUserId},a=await this.post("",t);return(0,i.u4)(a.data)}catch(e){s.zc.handleServiceErrorFromCatch(e,"Create venue")}}async updateVenue(e,t){try{let a={name:t.name,address:t.address,city:t.city},r=await this.put(`/${e}`,a);return(0,i.u4)(r.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Update venue (${e})`)}}async deleteVenue(e){try{await this.delete(`/${e}`)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Delete venue (${e})`)}}async getAllSeatsForVenue(e){try{return(await this.get(`/${e}/seats`)).data}catch(t){s.zc.handleServiceErrorFromCatch(t,`Fetch seats for venue (${e})`)}}async getAllSectionsForVenue(e){try{return(await this.get(`/${e}/sections`)).data.map(e=>({sectionId:e.sectionId,venueId:e.venueId,name:e.name,capacity:e.capacity,seats:e.seats?.map(e=>({seatId:e.seatId,sectionId:e.sectionId,seatNumber:e.seatNumber,rowNumber:e.rowNumber,seatInRow:e.seatInRow,status:"available"}))||[]}))}catch(t){s.zc.handleServiceErrorFromCatch(t,`Fetch sections for venue (${e})`)}}async getSectionById(e,t){try{let a=(await this.get(`/${e}/sections/${t}`)).data;return{sectionId:a.sectionId,venueId:a.venueId,name:a.name,capacity:a.capacity,seats:a.seats?.map(e=>({seatId:e.seatId,sectionId:e.sectionId,seatNumber:e.seatNumber,rowNumber:e.rowNumber,seatInRow:e.seatInRow,status:"available"}))||[]}}catch(a){s.zc.handleServiceErrorFromCatch(a,`Fetch section (${t}) for venue (${e})`)}}async createSection(e,t){try{let a={name:t.name,capacity:t.capacity,seats:t.seats.map(e=>({seatNumber:e.seatNumber,rowNumber:e.rowNumber,seatInRow:e.seatInRow}))},r=(await this.post(`/${e}/sections`,a)).data;return{sectionId:r.sectionId,venueId:r.venueId,name:r.name,capacity:r.capacity,seats:r.seats?.map(e=>({seatId:e.seatId,sectionId:e.sectionId,seatNumber:e.seatNumber,rowNumber:e.rowNumber,seatInRow:e.seatInRow,status:"available"}))||[]}}catch(t){s.zc.handleServiceErrorFromCatch(t,`Create section for venue (${e})`)}}async updateSection(e,t,a){try{let r={name:a.name,capacity:a.capacity,seats:a.seats.map(e=>({seatNumber:e.seatNumber,rowNumber:e.rowNumber,seatInRow:e.seatInRow}))},n=(await this.put(`/${e}/sections/${t}`,r)).data;return{sectionId:n.sectionId,venueId:n.venueId,name:n.name,capacity:n.capacity,seats:n.seats?.map(e=>({seatId:e.seatId,sectionId:e.sectionId,seatNumber:e.seatNumber,rowNumber:e.rowNumber,seatInRow:e.seatInRow,status:"available"}))||[]}}catch(a){s.zc.handleServiceErrorFromCatch(a,`Update section (${t}) for venue (${e})`)}}async deleteSection(e,t){try{await this.delete(`/${e}/sections/${t}`)}catch(a){s.zc.handleServiceErrorFromCatch(a,`Delete section (${t}) for venue (${e})`)}}}let d=new o,c=(0,r.zD)("venues/fetchAll",async(e,{rejectWithValue:t})=>{try{return await d.getAllVenues()}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),l=(0,r.zD)("venues/fetchById",async(e,{rejectWithValue:t})=>{try{return await d.getVenueById(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,r.zD)("venues/create",async(e,{rejectWithValue:t})=>{try{return await d.createVenue(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),h=(0,r.zD)("venues/update",async({venueId:e,venueData:t},{rejectWithValue:a})=>{try{return await d.updateVenue(e,t)}catch(e){return a(s.zc.handleAsyncThunkErrorFromCatch(e))}}),p=(0,r.zD)("venues/delete",async(e,{rejectWithValue:t})=>{try{return await d.deleteVenue(e),e}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),y=(0,r.zD)("venues/sections/fetchAll",async(e,{rejectWithValue:t})=>{try{let t=await d.getAllSectionsForVenue(e);return{venueId:e,sections:t}}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),m=(0,r.zD)("venues/sections/create",async({venueId:e,sectionData:t},{rejectWithValue:a})=>{try{return await d.createSection(e,t)}catch(e){return a(s.zc.handleAsyncThunkErrorFromCatch(e))}}),v=(0,r.zD)("venues/sections/update",async({venueId:e,sectionId:t,sectionData:a},{rejectWithValue:r})=>{try{return await d.updateSection(e,t,a)}catch(e){return r(s.zc.handleAsyncThunkErrorFromCatch(e))}}),g=(0,r.zD)("venues/sections/delete",async({venueId:e,sectionId:t},{rejectWithValue:a})=>{try{return await d.deleteSection(e,t),{venueId:e,sectionId:t}}catch(e){return a(s.zc.handleAsyncThunkErrorFromCatch(e))}}),A=(0,r.Z0)({name:"venues",initialState:{venues:[],currentVenue:null,isLoadingList:!1,isLoadingDetails:!1,isLoadingSections:!1,isLoadingMutation:!1,errorList:null,errorDetails:null,errorSections:null,errorMutation:null},reducers:{clearCurrentVenue:e=>{e.currentVenue=null,e.errorDetails=null,e.errorSections=null},clearVenueMutationError:e=>{e.errorMutation=null}},extraReducers:e=>{e.addCase(c.pending,e=>{e.isLoadingList=!0,e.errorList=null}).addCase(c.fulfilled,(e,t)=>{e.isLoadingList=!1,e.venues=t.payload}).addCase(c.rejected,(e,t)=>{e.isLoadingList=!1,e.errorList=t.payload}).addCase(l.pending,e=>{e.isLoadingDetails=!0,e.errorDetails=null,e.currentVenue=null}).addCase(l.fulfilled,(e,t)=>{e.isLoadingDetails=!1,e.currentVenue=t.payload,e.currentVenue&&void 0===e.currentVenue.sections&&y(e.currentVenue.venueId)}).addCase(l.rejected,(e,t)=>{e.isLoadingDetails=!1,e.errorDetails=t.payload}).addCase(u.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(u.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.venues.push(t.payload)}).addCase(u.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(h.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(h.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let a=e.venues.findIndex(e=>e.venueId===t.payload.venueId);-1!==a&&(e.venues[a]=t.payload),e.currentVenue?.venueId===t.payload.venueId&&(e.currentVenue=t.payload)}).addCase(h.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(p.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(p.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.venues=e.venues.filter(e=>e.venueId!==t.payload),e.currentVenue?.venueId===t.payload&&(e.currentVenue=null)}).addCase(p.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(y.pending,e=>{e.isLoadingSections=!0,e.errorSections=null}).addCase(y.fulfilled,(e,t)=>{e.isLoadingSections=!1,e.currentVenue?.venueId===t.payload.venueId&&(e.currentVenue.sections=t.payload.sections);let a=e.venues.find(e=>e.venueId===t.payload.venueId);a&&(a.sections=t.payload.sections)}).addCase(y.rejected,(e,t)=>{e.isLoadingSections=!1,e.errorSections=t.payload}).addCase(m.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(m.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.currentVenue?.venueId===t.payload.venueId&&(e.currentVenue.sections=[...e.currentVenue.sections||[],t.payload]);let a=e.venues.find(e=>e.venueId===t.payload.venueId);a&&(a.sections=[...a.sections||[],t.payload])}).addCase(m.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(v.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(v.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let{venueId:a,sectionId:r}=t.payload,n=e=>e?.map(e=>e.sectionId===r?t.payload:e);e.currentVenue?.venueId===a&&(e.currentVenue.sections=n(e.currentVenue.sections));let s=e.venues.find(e=>e.venueId===a);s&&(s.sections=n(s.sections))}).addCase(v.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(g.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(g.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let{venueId:a,sectionId:r}=t.payload,n=e=>e?.filter(e=>e.sectionId!==r)||[];e.currentVenue?.venueId===a&&(e.currentVenue.sections=n(e.currentVenue.sections));let s=e.venues.find(e=>e.venueId===a);s&&(s.sections=n(s.sections))}).addCase(g.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload})}}),{clearCurrentVenue:f,clearVenueMutationError:I}=A.actions,w=A.reducer},81415:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},82922:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Study\\\\Classes\\\\6thSemester\\\\SoftwareArchitecture\\\\project\\\\client\\\\src\\\\app\\\\provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\provider.tsx","default")},84233:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,85814,23)),Promise.resolve().then(a.t.bind(a,46533,23)),Promise.resolve().then(a.bind(a,32837)),Promise.resolve().then(a.bind(a,4384))},86835:(e,t,a)=>{"use strict";a.d(t,{zc:()=>i});var r=a(1510);function n(e,t,a){return{message:e,statusCode:t,details:a}}class s{static normalize(e){var t,a,r;return"type"in e&&e.type?e:e instanceof Error?{type:"UNKNOWN",message:e.message}:"object"==typeof(t=e)&&null!==t&&"statusCode"in t?{type:"API",message:e.message,code:e.code,statusCode:e.statusCode}:"object"==typeof(a=e)&&null!==a&&"isNetworkError"in a?{type:"NETWORK",message:e.message}:"object"==typeof(r=e)&&null!==r&&"field"in r?{type:"VALIDATION",message:e.message}:{type:"UNKNOWN",message:e.message||"An unknown error occurred"}}static getMessage(e){return"string"==typeof e?e:"message"in e?e.message:"An unknown error occurred"}}class i{static handleAxiosError(e){if(!e.response)return{message:e.message||"Network error occurred",isNetworkError:!0,originalError:e};let t=e.response,a=t.status,r=t.data;if(r?.message)return n(r.message,a,r.details);let s=t.data;if(s?.errors){let e=Object.entries(s.errors).map(([e,t])=>`${e}: ${t.join(", ")}`).join("; ");return n(`Validation failed: ${e}`,a,s.errors)}let i=t.data;return n(i?.message||i?.error||e.message||"API error occurred",a,i?.details)}static handleStandardError(e){return s.normalize(e)}static handle(e){return e instanceof r.pe?this.handleAxiosError(e):this.handleStandardError(e)}static log(e,t){let a=t?`[${t}]`:"";"statusCode"in e&&void 0!==e.statusCode?e.statusCode>=500?console.error(`${a} Server Error:`,e.message,e):e.statusCode>=400?console.warn(`${a} Client Error:`,e.message,e):console.warn(`${a} API Error:`,e.message,e):"isNetworkError"in e?console.error(`${a} Network Error:`,e.message,e):console.error(`${a} Error:`,e.message,e)}static handleServiceError(e,t){let a=this.handle(e);throw this.log(a,t),a}static handleAsyncThunkError(e){return this.handle(e).message}static handleUnknown(e){return e&&"object"==typeof e&&"isAxiosError"in e||e instanceof Error?e:Error("string"==typeof e?e:"An unknown error occurred")}static fromCatch(e){let t=this.handleUnknown(e);return this.handle(t)}static handleServiceErrorFromCatch(e,t){let a=this.handleUnknown(e);return this.handleServiceError(a,t)}static handleAsyncThunkErrorFromCatch(e){let t=this.handleUnknown(e);return this.handleAsyncThunkError(t)}}},98287:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},98680:(e,t,a)=>{"use strict";function r(e){return{userId:e.userId,userName:"",email:"",password:"",firstName:"",lastName:"",createdAt:new Date,updatedAt:new Date}}function n(e){let t=new Date(`${e.date}T${e.startTime}`),a=new Date(`${e.date}T${e.endTime}`);return{eventId:e.eventId,name:e.title,description:e.description||"",category:"Concert",startDateTime:t,endDateTime:a,venueId:e.venueId,venueName:"",venueAddress:"",poster:"",organizerUserId:e.organizerId,images:[],details:e.description||"",sectionPricing:e.sectionPricing.map(e=>({sectionId:e.sectionId,price:e.price})),status:e.status,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt)}}function s(e){let t,a;if(e.startDateTime&&e.endDateTime)t=e.startDateTime instanceof Date?e.startDateTime:new Date(e.startDateTime),a=e.endDateTime instanceof Date?e.endDateTime:new Date(e.endDateTime);else if(e.startDateTime&&e.endDateTime)t=new Date(e.startDateTime),a=new Date(e.endDateTime);else throw Error("Start and end date times are required");return{title:e.name||e.title||"",description:e.description,venueId:e.venueId||"",date:t.toISOString().split("T")[0],startTime:t.toTimeString().slice(0,5),endTime:a.toTimeString().slice(0,5),sectionPricing:e.sectionPricing||[]}}function i(e){return{venueId:e.venueId,name:e.name,address:e.address,city:e.city,ownerUserId:e.ownerUserId||"",sections:e.sections?.map(e=>({sectionId:e.sectionId,venueId:e.venueId,name:e.name,capacity:e.capacity,seats:e.seats?.map(e=>({seatId:e.seatId,sectionId:e.sectionId,seatNumber:e.seatNumber,rowNumber:e.rowNumber,seatInRow:e.seatInRow,status:"available"}))||[]}))||[],createdAt:e.createdAt,updatedAt:e.updatedAt}}function o(e){e.token&&(document.cookie=`token=${e.token}; path=/; ${e.expiration?`expires=${new Date(e.expiration).toUTCString()};`:""}`)}a.d(t,{Zm:()=>s,cR:()=>o,en:()=>n,mO:()=>r,u4:()=>i})}};