exports.id=695,exports.ids=[695],exports.modules={3377:(e,t,r)=>{"use strict";r.d(t,{i$:()=>A,TL:()=>y,HB:()=>f,b9:()=>C,lh:()=>l,Ay:()=>E,SX:()=>h,fw:()=>d,vR:()=>c,ls:()=>p,nK:()=>v,P_:()=>g,qM:()=>u});var a=r(9317),n=r(51060),s=r(86835);class o{parseEventDates(e){return{...e,createdAt:e.createdAt?new Date(e.createdAt):new Date,updatedAt:e.updatedAt?new Date(e.updatedAt):new Date,startDateTime:new Date(e.startDateTime),endDateTime:new Date(e.endDateTime)}}async getAllEvents(){try{return(await n.A.get(`${this.API_URL}/events`)).data.map(e=>this.parseEventDates(e))}catch{console.warn("Failed to fetch events from API, using sample data");let{sampleEvents:e}=await r.e(244).then(r.bind(r,84244));return e}}async createEvent(e){try{let t=await n.A.post(`${this.API_URL}/events`,e);return this.parseEventDates(t.data)}catch(e){s.zc.handleServiceErrorFromCatch(e,"Create event")}}async getEventById(e){try{let t=await n.A.get(`${this.API_URL}/events/${e}`);if(t.data)return this.parseEventDates(t.data);return null}catch(n){console.warn(`Failed to fetch event ${e} from API, trying sample data`);let{sampleEvents:t}=await r.e(244).then(r.bind(r,84244)),a=t.find(t=>t.eventId===e);if(a)return a;s.zc.handleServiceErrorFromCatch(n,`Fetch event by ID (${e})`)}}async rescheduleEvent(e,t){try{let r=await n.A.patch(`${this.API_URL}/events/${e}/reschedule`,t);return this.parseEventDates(r.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Reschedule event (${e})`)}}async deleteEvent(e){try{return(await n.A.delete(`${this.API_URL}/events/${e}`)).data}catch(t){s.zc.handleServiceErrorFromCatch(t,`Delete event (${e})`)}}async cancelEvent(e){try{let t=await n.A.patch(`${this.API_URL}/events/${e}/cancel`);return this.parseEventDates(t.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Cancel event (${e})`)}}async approveEvent(e){try{let t=await n.A.patch(`${this.API_URL}/events/${e}/approve`);return this.parseEventDates(t.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Approve event (${e})`)}}async postponeEvent(e){try{let t=await n.A.patch(`${this.API_URL}/events/${e}/postpone`);return this.parseEventDates(t.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Postpone event (${e})`)}}async submitEvent(e){try{let t=await n.A.patch(`${this.API_URL}/events/${e}/submit-for-approval`);return this.parseEventDates(t.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Submit event (${e})`)}}constructor(){this.API_URL=process.env.EVENT_API_URL||"http://localhost:8081",this.updateEvent=async(e,t)=>{try{let r=await n.A.patch(`${this.API_URL}/events/${e}`,t);return this.parseEventDates(r.data)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Update event (${e})`)}}}}let i=new o,d=(0,a.zD)("events/fetchAll",async(e,{rejectWithValue:t})=>{try{return await i.getAllEvents()}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),c=(0,a.zD)("events/fetchById",async(e,{rejectWithValue:t})=>{try{return await i.getEventById(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),l=(0,a.zD)("events/addNew",async(e,{rejectWithValue:t})=>{try{return await i.createEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,a.zD)("events/updateEvent",async({eventId:e,eventData:t},{rejectWithValue:r})=>{try{return await i.updateEvent(e,t)}catch(e){return r(s.zc.handleAsyncThunkErrorFromCatch(e))}}),h=(0,a.zD)("events/remove",async(e,{rejectWithValue:t})=>{try{return await i.deleteEvent(e),e}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),v=(0,a.zD)("events/reschedule",async({eventId:e,rescheduleData:t},{rejectWithValue:r})=>{try{return await i.rescheduleEvent(e,t)}catch(e){return r(s.zc.handleAsyncThunkErrorFromCatch(e))}}),p=(0,a.zD)("events/postpone",async(e,{rejectWithValue:t})=>{try{return await i.postponeEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),y=(0,a.zD)("events/cancel",async(e,{rejectWithValue:t})=>{try{return await i.cancelEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),A=(0,a.zD)("events/approve",async(e,{rejectWithValue:t})=>{try{return await i.approveEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),g=(0,a.zD)("events/submit",async(e,{rejectWithValue:t})=>{try{return await i.submitEvent(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),m=(0,a.Z0)({name:"events",initialState:{events:[],currentEvent:null,isLoadingList:!1,isLoadingDetails:!1,isLoadingMutation:!1,errorList:null,errorDetails:null,errorMutation:null},reducers:{clearCurrentEvent:e=>{e.currentEvent=null,e.errorDetails=null},clearMutationError:e=>{e.errorMutation=null}},extraReducers:e=>{e.addCase(d.pending,e=>{e.isLoadingList=!0,e.errorList=null}).addCase(d.fulfilled,(e,t)=>{e.isLoadingList=!1,e.events=t.payload}).addCase(d.rejected,(e,t)=>{e.isLoadingList=!1,e.errorList=t.payload}).addCase(c.pending,e=>{e.isLoadingDetails=!0,e.errorDetails=null,e.currentEvent=null}).addCase(c.fulfilled,(e,t)=>{e.isLoadingDetails=!1,e.currentEvent=t.payload}).addCase(c.rejected,(e,t)=>{e.isLoadingDetails=!1,e.errorDetails=t.payload}).addCase(l.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(l.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.events.push(t.payload)}).addCase(l.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(u.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(u.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let r=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==r&&(e.events[r]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(u.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(h.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(h.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.events=e.events.filter(e=>e.eventId!==t.payload),e.currentEvent?.eventId===t.payload&&(e.currentEvent=null)}).addCase(h.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(v.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(v.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let r=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==r&&(e.events[r]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(v.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(p.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(p.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let r=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==r&&(e.events[r]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(p.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(y.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(y.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let r=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==r&&(e.events[r]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(y.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(A.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(A.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let r=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==r&&(e.events[r]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(A.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(g.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(g.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let r=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==r&&(e.events[r]=t.payload),e.currentEvent?.eventId===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(g.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload})}}),{clearCurrentEvent:f,clearMutationError:C}=m.actions,E=m.reducer},4384:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var a=r(60687),n=r(9317),s=r(8866),o=r(3377),i=r(80991);let d=(0,n.Z0)({name:"booking",initialState:{selectedSeats:[],eventId:null,totalPrice:0},reducers:{setEventId:(e,t)=>{e.eventId=t.payload},addSeat:(e,t)=>{let{seat:r,price:a}=t.payload;e.selectedSeats.find(e=>e.seatId===r.seatId)||(e.selectedSeats.push(r),e.totalPrice+=a)},removeSeat:(e,t)=>{let{seatId:r,price:a}=t.payload;e.selectedSeats=e.selectedSeats.filter(e=>e.seatId!==r),e.totalPrice-=a},toggleSeat:(e,t)=>{let{seat:r,price:a}=t.payload,n=e.selectedSeats.findIndex(e=>e.seatId===r.seatId);-1!==n?(e.selectedSeats.splice(n,1),e.totalPrice-=a):(e.selectedSeats.push(r),e.totalPrice+=a)},clearSelectedSeats:e=>{e.selectedSeats=[],e.totalPrice=0},clearBooking:e=>{e.selectedSeats=[],e.eventId=null,e.totalPrice=0}}}),{setEventId:c,addSeat:l,removeSeat:u,toggleSeat:h,clearSelectedSeats:v,clearBooking:p}=d.actions,y=d.reducer,A=(0,n.U1)({reducer:{user:s.Ay,events:o.Ay,venues:i.Ay,booking:y},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"],ignoredActionsPaths:["meta.arg","payload.timestamp"],ignoredPaths:["items.dates"],isSerializable:e=>e instanceof Date||"object"!=typeof e||null===e||Array.isArray(e)||"[object Object]"===Object.prototype.toString.call(e)}})});r(43210);var g=r(54864);function m({children:e}){return(0,a.jsx)(g.Kq,{store:A,children:e})}},8866:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>p,Lx:()=>d,w2:()=>l,Ly:()=>v,E_:()=>c});var a=r(51060),n=r(86835);class s{async login(e){try{return(await a.A.post(`${this.API_URL}/auth/login`,e)).data}catch(e){n.zc.handleServiceErrorFromCatch(e,"Login")}}async signup(e){try{return(await a.A.post(`${this.API_URL}/auth/register`,e)).data}catch(e){n.zc.handleServiceErrorFromCatch(e,"Signup")}}async forgotPassword(e){try{return(await a.A.post(`${this.API_URL}/auth/forgot-password`,{email:e})).data}catch(e){n.zc.handleServiceErrorFromCatch(e,"Forgot password")}}constructor(){this.API_URL=process.env.NEXT_PUBLIC_BACKEND_API_URL||"http://localhost:8082/api"}}let o=new s;var i=r(9317);let d=(0,i.zD)("user/login",async(e,{rejectWithValue:t})=>{try{return await o.login(e)}catch(e){return t(n.zc.handleAsyncThunkErrorFromCatch(e))}}),c=(0,i.zD)("auth/signup",async(e,{rejectWithValue:t})=>{try{return await o.signup(e)}catch(e){return t(n.zc.handleAsyncThunkErrorFromCatch(e))}}),l=(0,i.zD)("auth/forgotPassword",async(e,{rejectWithValue:t})=>{try{return await o.forgotPassword(e)}catch(e){return t(n.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,i.Z0)({name:"user",initialState:{user:null,isLoading:!1,error:null,isAuthenticated:!1,forgotPasswordStatus:"idle",forgotPasswordError:null},reducers:{logout:e=>{e.user=null,e.isAuthenticated=!1,e.error=null},resetForgotPasswordState:e=>{e.forgotPasswordStatus="idle",e.forgotPasswordError=null}},extraReducers:e=>{e.addCase(d.pending,e=>{e.isLoading=!0,e.error=null}).addCase(d.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload,e.isAuthenticated=!0,e.error=null}).addCase(d.rejected,(e,t)=>{e.isLoading=!1,e.user=null,e.isAuthenticated=!1,e.error=t.payload||"Login failed"}).addCase(c.pending,e=>{e.isLoading=!0,e.error=null}).addCase(c.fulfilled,(e,t)=>{e.isLoading=!1,e.error=null,e.user=t.payload}).addCase(c.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload||"Signup failed"}).addCase(l.pending,e=>{e.forgotPasswordStatus="loading",e.forgotPasswordError=null}).addCase(l.fulfilled,e=>{e.forgotPasswordStatus="succeeded",e.forgotPasswordError=null}).addCase(l.rejected,(e,t)=>{e.forgotPasswordStatus="failed",e.forgotPasswordError=t.payload||"Failed to send reset link"})}}),{logout:h,resetForgotPasswordState:v}=u.actions,p=u.reducer},19081:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23)),Promise.resolve().then(r.bind(r,38917)),Promise.resolve().then(r.bind(r,82922))},32837:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a={src:"/_next/static/media/logo_app.9e88789c.png",height:39,width:228,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAABlBMVEU9PT0+Pj7oIj+7AAAAAnRSTlMteuZpD/UAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAMSURBVHicY2BkgAAAABEAAvIZoJkAAAAASUVORK5CYII=",blurWidth:8,blurHeight:1}},38917:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a={src:"/_next/static/media/logo_app.9e88789c.png",height:39,width:228,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAABlBMVEU9PT0+Pj7oIj+7AAAAAnRSTlMteuZpD/UAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAMSURBVHicY2BkgAAAABEAAvIZoJkAAAAASUVORK5CYII=",blurWidth:8,blurHeight:1}},53039:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,metadata:()=>h});var a=r(37413);r(61135);var n=r(27944);let s=r.n(n)().className;r(61120);var o=r(38917),i=r(53384),d=r(4536),c=r.n(d);function l(){return(0,a.jsxs)("div",{className:"w-full h-[70px] flex flex-row justify-between items-center px-8 border-b-1 border-b-gray-200",children:[(0,a.jsx)("div",{className:"logo-img w-[11%]",children:(0,a.jsx)(c(),{href:"/",children:(0,a.jsx)(i.default,{src:o.default,alt:"Logo",className:"w-full h-full block cursor-pointer"})})}),(0,a.jsxs)("div",{className:"flex flex-row items-center gap-6",children:[(0,a.jsx)(c(),{href:"/",className:"text-xl font-medium text-[16px] hover:bg-gray-100 rounded-xl p-2",children:"Home"}),(0,a.jsx)(c(),{href:"/",className:"text-xl font-medium text-[16px] hover:bg-gray-100 rounded-xl p-2",children:"My orders"})]})]})}var u=r(82922);let h={title:"Ticket Booking",description:"Where you can buy tickets",icons:{icon:"/logo.svg"}};function v({children:e}){return(0,a.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsx)(u.default,{children:(0,a.jsxs)("body",{className:"w-full min-h-screen",children:[(0,a.jsx)(l,{}),(0,a.jsx)("main",{className:`${s}`,children:e})]})})})}},61135:()=>{},80991:(e,t,r)=>{"use strict";r.d(t,{Mo:()=>m,Ay:()=>C,Of:()=>d,rh:()=>v,qT:()=>c});var a=r(9317),n=r(51060),s=r(86835);class o{async getAllVenues(){try{return(await n.A.get(`${this.API_URL}/Venues`)).data}catch(e){s.zc.handleServiceErrorFromCatch(e,"Fetch venues")}}async getVenueById(e){try{return(await n.A.get(`${this.API_URL}/Venues/${e}`)).data}catch(e){s.zc.handleServiceErrorFromCatch(e,"Fetch venue by ID")}}async createVenue(e){try{return(await n.A.post(`${this.API_URL}/Venues`,e)).data}catch(e){s.zc.handleServiceErrorFromCatch(e,"Create venue")}}async updateVenue(e,t){try{return(await n.A.put(`${this.API_URL}/Venues/${e}`,t)).data}catch(t){s.zc.handleServiceErrorFromCatch(t,`Update venue (${e})`)}}async deleteVenue(e){try{await n.A.delete(`${this.API_URL}/Venues/${e}`)}catch(t){s.zc.handleServiceErrorFromCatch(t,`Delete venue (${e})`)}}async getAllSectionsForVenue(e){try{return(await n.A.get(`${this.API_URL}/venues/${e}/sections`)).data}catch(t){s.zc.handleServiceErrorFromCatch(t,`Fetch sections for venue (${e})`)}}async getSectionById(e,t){try{return(await n.A.get(`${this.API_URL}/venues/${e}/sections/${t}`)).data}catch(r){s.zc.handleServiceErrorFromCatch(r,`Fetch section (${t}) for venue (${e})`)}}async createSection(e,t){try{return(await n.A.post(`${this.API_URL}/venues/${e}/sections`,t)).data}catch(t){s.zc.handleServiceErrorFromCatch(t,`Create section for venue (${e})`)}}async updateSection(e,t,r){try{return(await n.A.put(`${this.API_URL}/venues/${e}/sections/${t}`,r)).data}catch(r){s.zc.handleServiceErrorFromCatch(r,`Update section (${t}) for venue (${e})`)}}async deleteSection(e,t){try{await n.A.delete(`${this.API_URL}/venues/${e}/sections/${t}`)}catch(r){s.zc.handleServiceErrorFromCatch(r,`Delete section (${t}) for venue (${e})`)}}constructor(){this.API_URL=process.env.NEXT_PUBLIC_BACKEND_API_URL||"http://localhost:8083/api"}}let i=new o,d=(0,a.zD)("venues/fetchAll",async(e,{rejectWithValue:t})=>{try{return await i.getAllVenues()}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),c=(0,a.zD)("venues/fetchById",async(e,{rejectWithValue:t})=>{try{return await i.getVenueById(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),l=(0,a.zD)("venues/create",async(e,{rejectWithValue:t})=>{try{return await i.createVenue(e)}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,a.zD)("venues/update",async({venueId:e,venueData:t},{rejectWithValue:r})=>{try{return await i.updateVenue(e,t)}catch(e){return r(s.zc.handleAsyncThunkErrorFromCatch(e))}}),h=(0,a.zD)("venues/delete",async(e,{rejectWithValue:t})=>{try{return await i.deleteVenue(e),e}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),v=(0,a.zD)("venues/sections/fetchAll",async(e,{rejectWithValue:t})=>{try{let t=await i.getAllSectionsForVenue(e);return{venueId:e,sections:t}}catch(e){return t(s.zc.handleAsyncThunkErrorFromCatch(e))}}),p=(0,a.zD)("venues/sections/create",async({venueId:e,sectionData:t},{rejectWithValue:r})=>{try{return await i.createSection(e,t)}catch(e){return r(s.zc.handleAsyncThunkErrorFromCatch(e))}}),y=(0,a.zD)("venues/sections/update",async({venueId:e,sectionId:t,sectionData:r},{rejectWithValue:a})=>{try{return await i.updateSection(e,t,r)}catch(e){return a(s.zc.handleAsyncThunkErrorFromCatch(e))}}),A=(0,a.zD)("venues/sections/delete",async({venueId:e,sectionId:t},{rejectWithValue:r})=>{try{return await i.deleteSection(e,t),{venueId:e,sectionId:t}}catch(e){return r(s.zc.handleAsyncThunkErrorFromCatch(e))}}),g=(0,a.Z0)({name:"venues",initialState:{venues:[],currentVenue:null,isLoadingList:!1,isLoadingDetails:!1,isLoadingSections:!1,isLoadingMutation:!1,errorList:null,errorDetails:null,errorSections:null,errorMutation:null},reducers:{clearCurrentVenue:e=>{e.currentVenue=null,e.errorDetails=null,e.errorSections=null},clearVenueMutationError:e=>{e.errorMutation=null}},extraReducers:e=>{e.addCase(d.pending,e=>{e.isLoadingList=!0,e.errorList=null}).addCase(d.fulfilled,(e,t)=>{e.isLoadingList=!1,e.venues=t.payload}).addCase(d.rejected,(e,t)=>{e.isLoadingList=!1,e.errorList=t.payload}).addCase(c.pending,e=>{e.isLoadingDetails=!0,e.errorDetails=null,e.currentVenue=null}).addCase(c.fulfilled,(e,t)=>{e.isLoadingDetails=!1,e.currentVenue=t.payload,e.currentVenue&&void 0===e.currentVenue.sections&&v(e.currentVenue.venueId)}).addCase(c.rejected,(e,t)=>{e.isLoadingDetails=!1,e.errorDetails=t.payload}).addCase(l.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(l.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.venues.push(t.payload)}).addCase(l.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(u.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(u.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let r=e.venues.findIndex(e=>e.venueId===t.payload.venueId);-1!==r&&(e.venues[r]=t.payload),e.currentVenue?.venueId===t.payload.venueId&&(e.currentVenue=t.payload)}).addCase(u.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(h.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(h.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.venues=e.venues.filter(e=>e.venueId!==t.payload),e.currentVenue?.venueId===t.payload&&(e.currentVenue=null)}).addCase(h.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(v.pending,e=>{e.isLoadingSections=!0,e.errorSections=null}).addCase(v.fulfilled,(e,t)=>{e.isLoadingSections=!1,e.currentVenue?.venueId===t.payload.venueId&&(e.currentVenue.sections=t.payload.sections);let r=e.venues.find(e=>e.venueId===t.payload.venueId);r&&(r.sections=t.payload.sections)}).addCase(v.rejected,(e,t)=>{e.isLoadingSections=!1,e.errorSections=t.payload}).addCase(p.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(p.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.currentVenue?.venueId===t.payload.venueId&&(e.currentVenue.sections=[...e.currentVenue.sections||[],t.payload]);let r=e.venues.find(e=>e.venueId===t.payload.venueId);r&&(r.sections=[...r.sections||[],t.payload])}).addCase(p.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(y.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(y.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let{venueId:r,sectionId:a}=t.payload,n=e=>e?.map(e=>e.sectionId===a?t.payload:e);e.currentVenue?.venueId===r&&(e.currentVenue.sections=n(e.currentVenue.sections));let s=e.venues.find(e=>e.venueId===r);s&&(s.sections=n(s.sections))}).addCase(y.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(A.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(A.fulfilled,(e,t)=>{e.isLoadingMutation=!1;let{venueId:r,sectionId:a}=t.payload,n=e=>e?.filter(e=>e.sectionId!==a)||[];e.currentVenue?.venueId===r&&(e.currentVenue.sections=n(e.currentVenue.sections));let s=e.venues.find(e=>e.venueId===r);s&&(s.sections=n(s.sections))}).addCase(A.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload})}}),{clearCurrentVenue:m,clearVenueMutationError:f}=g.actions,C=g.reducer},81415:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},82922:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Study\\\\Classes\\\\6thSemester\\\\SoftwareArchitecture\\\\project\\\\client\\\\src\\\\app\\\\provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\provider.tsx","default")},84233:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23)),Promise.resolve().then(r.bind(r,32837)),Promise.resolve().then(r.bind(r,4384))},86835:(e,t,r)=>{"use strict";r.d(t,{zc:()=>s});var a=r(1510);class n{static normalize(e){var t,r,a;return"type"in e&&e.type?e:e instanceof Error?{type:"UNKNOWN",message:e.message}:"object"==typeof(t=e)&&null!==t&&"statusCode"in t?{type:"API",message:e.message,code:e.code,statusCode:e.statusCode}:"object"==typeof(r=e)&&null!==r&&"isNetworkError"in r?{type:"NETWORK",message:e.message}:"object"==typeof(a=e)&&null!==a&&"field"in a?{type:"VALIDATION",message:e.message}:{type:"UNKNOWN",message:e.message||"An unknown error occurred"}}static getMessage(e){return"string"==typeof e?e:"message"in e?e.message:"An unknown error occurred"}}class s{static handleAxiosError(e){if(!e.response)return{message:e.message||"Network error occurred",isNetworkError:!0,originalError:e};let t=e.response,r=t.data,a=r?.message||r?.error||e.message||"API error occurred",n=t.status;return{message:a,statusCode:n,details:r?.details}}static handleStandardError(e){return n.normalize(e)}static handle(e){return e instanceof a.pe?this.handleAxiosError(e):this.handleStandardError(e)}static log(e,t){let r=t?`[${t}]`:"";"statusCode"in e&&void 0!==e.statusCode?e.statusCode>=500?console.error(`${r} Server Error:`,e.message,e):e.statusCode>=400?console.warn(`${r} Client Error:`,e.message,e):console.warn(`${r} API Error:`,e.message,e):"isNetworkError"in e?console.error(`${r} Network Error:`,e.message,e):console.error(`${r} Error:`,e.message,e)}static handleServiceError(e,t){let r=this.handle(e);throw this.log(r,t),r}static handleAsyncThunkError(e){return this.handle(e).message}static handleUnknown(e){return e&&"object"==typeof e&&"isAxiosError"in e||e instanceof Error?e:Error("string"==typeof e?e:"An unknown error occurred")}static fromCatch(e){let t=this.handleUnknown(e);return this.handle(t)}static handleServiceErrorFromCatch(e,t){let r=this.handleUnknown(e);return this.handleServiceError(r,t)}static handleAsyncThunkErrorFromCatch(e){let t=this.handleUnknown(e);return this.handleAsyncThunkError(t)}}},98287:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))}};