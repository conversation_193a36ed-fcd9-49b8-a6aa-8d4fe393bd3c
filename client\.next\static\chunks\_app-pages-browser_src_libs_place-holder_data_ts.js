"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_libs_place-holder_data_ts"],{

/***/ "(app-pages-browser)/./src/libs/place-holder.data.ts":
/*!***************************************!*\
  !*** ./src/libs/place-holder.data.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sampleEvents: () => (/* binding */ sampleEvents),\n/* harmony export */   sampleSeats: () => (/* binding */ sampleSeats)\n/* harmony export */ });\n/* harmony import */ var _models_Event__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/Event */ \"(app-pages-browser)/./src/models/Event.ts\");\n\nconst sampleEvents = [\n    {\n        eventId: \"1a2b3c4d5e6f7g8h9i0j\",\n        name: \"Music Festival 2025\",\n        description: \"A grand music festival featuring top artists from around the world.\",\n        category: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventCategory.CONCERT,\n        startDateTime: new Date(\"2025-05-01T18:00:00Z\"),\n        endDateTime: new Date(\"2025-05-01T23:00:00Z\"),\n        status: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.PUBLISHED,\n        venueId: \"venue123\",\n        venueName: \"Madison Square Garden\",\n        venueAddress: \"4 Pennsylvania Plaza, New York, NY 10001\",\n        organizerUserId: \"user456\",\n        poster: \"https://images.unsplash.com/photo-1558465202-92356bf74344?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D\",\n        images: [\n            \"https://images.unsplash.com/photo-1558465202-92356bf74344?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D\",\n            \"https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?q=80&w=2070&auto=format&fit=crop\"\n        ],\n        details: \"Join us for an unforgettable night of music featuring world-renowned artists. This festival will showcase diverse musical genres and provide an amazing experience for all attendees.\",\n        createdAt: new Date(\"2025-04-01T10:00:00Z\"),\n        updatedAt: new Date(\"2025-04-10T12:00:00Z\"),\n        sectionPricing: [\n            {\n                eventId: \"1a2b3c4d5e6f7g8h9i0j\",\n                sectionId: \"section-1\",\n                price: 50\n            },\n            {\n                eventId: \"1a2b3c4d5e6f7g8h9i0j\",\n                sectionId: \"section-2\",\n                price: 75\n            },\n            {\n                eventId: \"1a2b3c4d5e6f7g8h9i0j\",\n                sectionId: \"section-3\",\n                price: 100\n            }\n        ]\n    },\n    {\n        eventId: \"2b3c4d5e6f7g8h9i0j1a\",\n        name: \"Tech Conference 2025\",\n        description: \"An annual conference showcasing the latest in technology and innovation.\",\n        category: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventCategory.OTHERS,\n        startDateTime: new Date(\"2025-06-15T09:00:00Z\"),\n        endDateTime: new Date(\"2025-06-15T17:00:00Z\"),\n        status: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.PUBLISHED,\n        venueId: \"venue456\",\n        venueName: \"Convention Center\",\n        venueAddress: \"123 Tech Street, San Francisco, CA 94102\",\n        organizerUserId: \"user789\",\n        poster: \"https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=2070&auto=format&fit=crop\",\n        images: [\n            \"https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=2070&auto=format&fit=crop\",\n            \"https://images.unsplash.com/photo-1531482615713-2afd69097998?q=80&w=2070&auto=format&fit=crop\"\n        ],\n        details: \"Discover the latest trends in technology, network with industry leaders, and attend workshops on cutting-edge innovations.\",\n        createdAt: new Date(\"2025-03-20T14:00:00Z\"),\n        updatedAt: new Date(\"2025-04-05T16:00:00Z\"),\n        sectionPricing: [\n            {\n                eventId: \"2b3c4d5e6f7g8h9i0j1a\",\n                sectionId: \"section-1\",\n                price: 150\n            },\n            {\n                eventId: \"2b3c4d5e6f7g8h9i0j1a\",\n                sectionId: \"section-2\",\n                price: 200\n            }\n        ]\n    },\n    {\n        eventId: \"3c4d5e6f7g8h9i0j1a2b\",\n        name: \"Art Exhibition 2025\",\n        description: \"A showcase of contemporary art from renowned artists.\",\n        category: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventCategory.OTHERS,\n        startDateTime: new Date(\"2025-07-10T10:00:00Z\"),\n        endDateTime: new Date(\"2025-07-20T18:00:00Z\"),\n        status: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.PUBLISHED,\n        venueId: \"venue789\",\n        venueName: \"Metropolitan Art Gallery\",\n        venueAddress: \"456 Art Avenue, New York, NY 10021\",\n        organizerUserId: \"user123\",\n        poster: \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?q=80&w=2070&auto=format&fit=crop\",\n        images: [\n            \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?q=80&w=2070&auto=format&fit=crop\",\n            \"https://images.unsplash.com/photo-1541961017774-22349e4a1262?q=80&w=2070&auto=format&fit=crop\"\n        ],\n        details: \"Experience contemporary art from around the world. This exhibition features paintings, sculptures, and digital art from emerging and established artists.\",\n        createdAt: new Date(\"2025-02-15T09:00:00Z\"),\n        updatedAt: new Date(\"2025-03-01T11:00:00Z\"),\n        sectionPricing: [\n            {\n                eventId: \"3c4d5e6f7g8h9i0j1a2b\",\n                sectionId: \"section-1\",\n                price: 25\n            },\n            {\n                eventId: \"3c4d5e6f7g8h9i0j1a2b\",\n                sectionId: \"section-2\",\n                price: 35\n            }\n        ]\n    },\n    {\n        eventId: \"4d5e6f7g8h9i0j1a2b3c\",\n        name: \"Championship Football Match\",\n        description: \"The ultimate showdown between two legendary teams.\",\n        category: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventCategory.MATCH,\n        startDateTime: new Date(\"2025-08-05T19:30:00Z\"),\n        endDateTime: new Date(\"2025-08-05T22:00:00Z\"),\n        status: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.PUBLISHED,\n        venueId: \"venue321\",\n        venueName: \"America First Field\",\n        venueAddress: \"9256 S State St, Sandy, UT 84070\",\n        organizerUserId: \"user654\",\n        poster: \"https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?q=80&w=2070&auto=format&fit=crop\",\n        images: [\n            \"https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?q=80&w=2070&auto=format&fit=crop\",\n            \"https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=2070&auto=format&fit=crop\"\n        ],\n        details: \"Don't miss this epic championship match! Watch as two powerhouse teams battle it out for the ultimate prize in this season-defining game.\",\n        createdAt: new Date(\"2025-04-05T08:00:00Z\"),\n        updatedAt: new Date(\"2025-04-12T10:00:00Z\"),\n        sectionPricing: [\n            {\n                eventId: \"4d5e6f7g8h9i0j1a2b3c\",\n                sectionId: \"section-1\",\n                price: 80\n            },\n            {\n                eventId: \"4d5e6f7g8h9i0j1a2b3c\",\n                sectionId: \"section-2\",\n                price: 120\n            },\n            {\n                eventId: \"4d5e6f7g8h9i0j1a2b3c\",\n                sectionId: \"section-3\",\n                price: 150\n            }\n        ]\n    },\n    {\n        eventId: \"5e6f7g8h9i0j1a2b3c4d\",\n        name: \"Charity Run 2025\",\n        description: \"A charity run event to raise funds for local communities.\",\n        category: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventCategory.OTHERS,\n        startDateTime: new Date(\"2025-09-10T06:00:00Z\"),\n        endDateTime: new Date(\"2025-09-10T12:00:00Z\"),\n        status: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.DRAFT,\n        venueId: \"venue654\",\n        venueName: \"Central Park\",\n        venueAddress: \"Central Park, New York, NY 10024\",\n        organizerUserId: \"user987\",\n        poster: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2070&auto=format&fit=crop\",\n        images: [\n            \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2070&auto=format&fit=crop\",\n            \"https://images.unsplash.com/photo-**********-fa95b6ee9643?q=80&w=2070&auto=format&fit=crop\"\n        ],\n        details: \"Join us for a meaningful charity run to support local communities. All proceeds will go towards education and healthcare initiatives.\",\n        createdAt: new Date(\"2025-01-10T07:00:00Z\"),\n        updatedAt: new Date(\"2025-02-20T09:00:00Z\"),\n        sectionPricing: [\n            {\n                eventId: \"5e6f7g8h9i0j1a2b3c4d\",\n                sectionId: \"section-1\",\n                price: 30\n            }\n        ]\n    }\n];\nconst sampleSeats = [\n    {\n        SeatId: \"Seat 1\",\n        SectionId: \"Section A1\",\n        SeatNumber: \"Seat 1\",\n        RowNumber: \"Row 1\",\n        SeatInRow: 10\n    },\n    {\n        SeatId: \"Seat 2\",\n        SectionId: \"Section A2\",\n        SeatNumber: \"Seat 2\",\n        RowNumber: \"Row 2\",\n        SeatInRow: 20\n    },\n    {\n        SeatId: \"Seat 3\",\n        SectionId: \"Section A3\",\n        SeatNumber: \"Seat 3\",\n        RowNumber: \"Row 3\",\n        SeatInRow: 30\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/libs/place-holder.data.ts\n"));

/***/ })

}]);