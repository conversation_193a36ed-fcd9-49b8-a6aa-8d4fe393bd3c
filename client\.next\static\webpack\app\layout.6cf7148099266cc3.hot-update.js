"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"98355a906007\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcU3R1ZHlcXENsYXNzZXNcXDZ0aFNlbWVzdGVyXFxTb2Z0d2FyZUFyY2hpdGVjdHVyZVxccHJvamVjdFxcY2xpZW50XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5ODM1NWE5MDYwMDdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/authService.ts":
/*!*************************************!*\
  !*** ./src/services/authService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/errorHandler */ \"(app-pages-browser)/./src/utils/errorHandler.ts\");\n/* harmony import */ var _baseService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./baseService */ \"(app-pages-browser)/./src/services/baseService.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\nclass AuthService extends _baseService__WEBPACK_IMPORTED_MODULE_1__[\"default\"] {\n    async login(credentials) {\n        try {\n            const response = await this.post(\"/login\", credentials);\n            return response.data;\n        } catch (error) {\n            _utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.ErrorHandler.handleServiceErrorFromCatch(error, \"Login\");\n        }\n    }\n    async signup(userData) {\n        try {\n            const response = await this.post(\"/register\", userData);\n            return response.data;\n        } catch (error) {\n            _utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.ErrorHandler.handleServiceErrorFromCatch(error, \"Signup\");\n        }\n    }\n    async forgotPassword(forgotPasswordData) {\n        try {\n            const response = await this.post(\"/forgot-password\", forgotPasswordData);\n            return response.data;\n        } catch (error) {\n            _utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.ErrorHandler.handleServiceErrorFromCatch(error, \"Forgot password\");\n        }\n    }\n    constructor(){\n        super(\"/auth\", {\n            baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8080\",\n            enableAuth: false\n        });\n    }\n}\nconst authService = new AuthService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/authService.ts\n"));

/***/ })

});