{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 2100", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@radix-ui/react-slot": "^1.2.0", "@reduxjs/toolkit": "^2.8.2", "@types/js-cookie": "^3.0.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.487.0", "next": "15.3.0", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "prettier": "^3.5.3", "tailwindcss": "^4.1.3", "typescript": "^5"}}