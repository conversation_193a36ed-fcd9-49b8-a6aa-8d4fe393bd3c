import BaseService from "./BaseService";
import Event from "../models/Event";
import { CreateEventDTO, RescheduleEventDTO, UpdateEventDTO } from "../models/DTO/EventDTO";
import { <PERSON>rrorHandler } from "@/utils/errorHandler";
import {
  ApiEvent,
  CreateEventRequest,
  UpdateEventRequest,
  RescheduleEventRequest,
} from "@/types/api";
import { adaptApiEventToEvent, adaptEventToCreateRequest } from "@/utils/apiAdapters";

class EventService extends BaseService {
  constructor() {
    super("/api/events", {
      enableAuth: true, // Event management requires authentication
    });
  }

  parseEventDates(
    eventData: Partial<Event> & { startDateTime: string | Date; endDateTime: string | Date }
  ): Event {
    return {
      ...eventData,
      // Đảm bảo createdAt và updatedAt cũng được parse nếu chúng tồn tại và là string date
      createdAt: eventData.createdAt ? new Date(eventData.createdAt) : new Date(),
      updatedAt: eventData.updatedAt ? new Date(eventData.updatedAt) : new Date(),
      startDateTime: new Date(eventData.startDateTime),
      endDateTime: new Date(eventData.endDateTime),
    } as Event;
  }

  async getAllEvents(): Promise<Event[]> {
    try {
      const response = await this.get<ApiEvent[]>("");
      return response.data.map(apiEvent => adaptApiEventToEvent(apiEvent));
    } catch {
      console.warn("Failed to fetch events from API, using sample data");
      return [];
    }
  }

  async getPublishedEvents(): Promise<Event[]> {
    try {
      const response = await this.get<ApiEvent[]>("/published");
      return response.data.map(apiEvent => adaptApiEventToEvent(apiEvent));
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Fetch published events");
    }
  }

  async createEvent(eventData: CreateEventDTO): Promise<Event> {
    try {
      // Convert frontend DTO to backend API request
      const createRequest: CreateEventRequest = adaptEventToCreateRequest(eventData);
      const response = await this.post<ApiEvent>("", createRequest);
      return adaptApiEventToEvent(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Create event");
    }
  }

  async getEventById(eventId: string): Promise<Event | null> {
    try {
      const response = await this.get<ApiEvent>(`/${eventId}`);
      if (response.data) {
        return adaptApiEventToEvent(response.data);
      }
      return null;
    } catch (error) {
      console.warn(`Failed to fetch event ${eventId} from API, trying sample data`);

      // Fallback to sample data for development
      const { sampleEvents } = await import("@/libs/place-holder.data");
      const sampleEvent = sampleEvents.find(event => event.eventId === eventId);
      if (sampleEvent) {
        return sampleEvent;
      }

      ErrorHandler.handleServiceErrorFromCatch(error, `Fetch event by ID (${eventId})`);
    }
  }

  updateEvent = async (eventId: string, eventData: UpdateEventDTO): Promise<Event> => {
    try {
      // Convert frontend DTO to backend API request
      const updateRequest: UpdateEventRequest = {
        title: eventData.name,
        description: eventData.description,
        // Note: UpdateEventDTO doesn't include sectionPricing, date/time changes
        // Those are handled by separate endpoints (reschedule, etc.)
      };
      const response = await this.patch<ApiEvent>(`/${eventId}`, updateRequest);
      return adaptApiEventToEvent(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Update event (${eventId})`);
    }
  };

  async rescheduleEvent(eventId: string, rescheduleEventDTO: RescheduleEventDTO): Promise<Event> {
    try {
      // Convert frontend DTO to backend API request
      const rescheduleRequest: RescheduleEventRequest = {
        date: new Date(rescheduleEventDTO.newStartDateTime).toISOString().split("T")[0],
        startTime: new Date(rescheduleEventDTO.newStartDateTime).toTimeString().slice(0, 5),
        endTime: new Date(rescheduleEventDTO.newEndDateTime).toTimeString().slice(0, 5),
      };
      const response = await this.patch<ApiEvent>(`/${eventId}/reschedule`, rescheduleRequest);
      return adaptApiEventToEvent(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Reschedule event (${eventId})`);
    }
  }

  async deleteEvent(eventId: string): Promise<void> {
    try {
      await this.delete(`/${eventId}`);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Delete event (${eventId})`);
    }
  }

  async cancelEvent(eventId: string): Promise<Event> {
    try {
      const response = await this.patch<ApiEvent>(`/${eventId}/cancel`);
      return adaptApiEventToEvent(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Cancel event (${eventId})`);
    }
  }

  async approveEvent(eventId: string): Promise<Event> {
    try {
      const response = await this.patch<ApiEvent>(`/${eventId}/approve`);
      return adaptApiEventToEvent(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Approve event (${eventId})`);
    }
  }

  async postponeEvent(eventId: string): Promise<Event> {
    try {
      const response = await this.patch<ApiEvent>(`/${eventId}/postpone`);
      return adaptApiEventToEvent(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Postpone event (${eventId})`);
    }
  }

  async submitEvent(eventId: string): Promise<Event> {
    try {
      const response = await this.patch<ApiEvent>(`/${eventId}/submit-for-approval`);
      return adaptApiEventToEvent(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Submit event (${eventId})`);
    }
  }
}

const eventService = new EventService();

export default eventService;
