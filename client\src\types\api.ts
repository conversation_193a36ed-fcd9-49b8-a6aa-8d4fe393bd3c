// Backend API Response Types and Models
// Based on the microservices API documentation

// ============================================================================
// AUTHENTICATION & USER TYPES
// ============================================================================

export interface AuthResponse {
  isSuccess: boolean;
  message?: string;
  token?: string; // JWT token
  expiration?: string; // ISO date string
  userId?: string;
}

export interface RegisterRequest {
  email: string; // Required, valid email format
  username: string; // Required, 3-50 characters
  password: string; // Required, minimum 6 characters
  role: "User" | "Organizer" | "Admin"; // Required
}

export interface LoginRequest {
  email: string; // Required
  password: string; // Required
}

export interface UpdateUserRequest {
  firstName?: string; // Optional, max 100 characters
  lastName?: string; // Optional, max 100 characters
  email?: string; // Optional, valid email format
}

export interface ApiUser {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: string; // ISO date string
}

// ============================================================================
// EVENT TYPES
// ============================================================================

export interface ApiEvent {
  eventId: string;
  title: string;
  description?: string;
  venueId: string;
  date: string; // YYYY-MM-DD format
  startTime: string; // HH:mm format (24-hour)
  endTime: string; // HH:mm format (24-hour)
  status: "Draft" | "Pending" | "Approved" | "Cancelled" | "Postponed" | "Rescheduled";
  organizerId: string;
  sectionPricing: SectionPricing[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface CreateEventRequest {
  title: string; // Required, max 200 characters
  description?: string; // Optional, max 1000 characters
  venueId: string; // Required, valid GUID
  date: string; // Required, YYYY-MM-DD, must be future date
  startTime: string; // Required, HH:mm format
  endTime: string; // Required, HH:mm format, must be after startTime
  sectionPricing: SectionPricing[]; // Required, at least one section
}

export interface UpdateEventRequest {
  title?: string; // Optional, max 200 characters
  description?: string; // Optional, max 1000 characters
  date?: string; // Optional, YYYY-MM-DD, must be future date
  startTime?: string; // Optional, HH:mm format
  endTime?: string; // Optional, HH:mm format, must be after startTime
  sectionPricing?: SectionPricing[]; // Optional
}

export interface RescheduleEventRequest {
  date: string; // Required, YYYY-MM-DD, must be future date
  startTime: string; // Required, HH:mm format
  endTime: string; // Required, HH:mm format, must be after startTime
}

export interface SectionPricing {
  sectionId: string; // Required, valid GUID
  price: number; // Required, minimum 0, maximum 999999.99
}

// ============================================================================
// VENUE TYPES
// ============================================================================

export interface ApiVenue {
  venueId: string;
  name: string;
  address: string;
  city: string;
  ownerUserId?: string;
  sections?: ApiSection[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface CreateVenueRequest {
  name: string; // Required, max 100 characters
  address: string; // Required, max 255 characters
  city: string; // Required, max 100 characters
  ownerUserId?: string; // Optional, valid GUID
}

export interface UpdateVenueRequest {
  name?: string; // Optional, max 100 characters
  address?: string; // Optional, max 255 characters
  city?: string; // Optional, max 100 characters
}

export interface ApiSection {
  sectionId: string;
  venueId: string;
  name: string;
  capacity: number;
  seats?: ApiSeat[];
}

export interface CreateSectionRequest {
  name: string; // Required, max 50 characters
  capacity: number; // Required, minimum 1, maximum 10000
  seats: CreateSeatRequest[]; // Required, must match capacity
}

export interface ApiSeat {
  seatId: string;
  sectionId: string;
  seatNumber: string;
  rowNumber: string;
  seatInRow: number;
}

export interface CreateSeatRequest {
  seatNumber: string; // Required, max 10 characters
  rowNumber: string; // Required, max 5 characters
  seatInRow: number; // Required, minimum 1
}

// ============================================================================
// BOOKING TYPES
// ============================================================================

export interface ApiBooking {
  id: string;
  eventId: string;
  totalPrice: number;
  status: "Pending" | "Confirmed" | "Cancelled" | "Expired";
  userId?: string;
  expiresAt?: string; // ISO date string
  createdAt: string; // ISO date string
}

export interface CreateBookingRequest {
  eventId: string; // Required, valid GUID
  seatIds: string[]; // Required, array of valid seat GUIDs, max 10 seats
}

export interface BookingResponse {
  bookingId: string;
  status: string;
  totalPrice: number;
  bookingInitiatedAt: string; // ISO date string
  paymentDeadline: string; // ISO date string
}

export interface SeatStatus {
  seatId: string;
  status: "Available" | "Reserved" | "Booked";
  price: number;
  reservedUntil?: string; // ISO date string
}

// ============================================================================
// PAYMENT TYPES
// ============================================================================

export interface PaymentRequest {
  bookingId: string; // Required, valid GUID
  amount: number; // Required, minimum 0.01
  currency: string; // Required, 'VND' | 'USD'
  gateway: "VnPay" | "Stripe" | "PayPal"; // Required
  returnUrl: string; // Required, valid URL
  cancelUrl: string; // Required, valid URL
}

export interface PaymentResponse {
  paymentUrl: string; // URL to redirect user for payment
  paymentId: string;
  expiresAt: string; // ISO date string
}

export interface PaymentStatus {
  paymentId: string;
  status: "Pending" | "Completed" | "Failed" | "Cancelled";
  amount: number;
  currency: string;
  completedAt?: string; // ISO date string
}

// ============================================================================
// ERROR RESPONSE TYPES
// ============================================================================

export interface ApiErrorResponse {
  message: string;
  details?: string;
  timestamp: string; // ISO date string
  path: string; // API endpoint path
}

export interface ValidationErrorResponse {
  message: string; // "Validation failed"
  errors: {
    [fieldName: string]: string[];
  };
  timestamp: string; // ISO date string
  path: string; // API endpoint path
}
