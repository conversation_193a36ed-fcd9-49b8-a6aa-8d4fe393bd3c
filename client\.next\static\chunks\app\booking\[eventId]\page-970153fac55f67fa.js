(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[208],{285:(e,t,i)=>{"use strict";i.d(t,{$:()=>o});var n=i(5155);i(2115);var s=i(4624),a=i(2085),r=i(6707);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-darkText shadow-xs hover:bg-primary/70",outline:"border bg-transparent border-darkStroke shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:i,size:a,asChild:o=!1,...c}=e,d=o?s.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,r.cn)(l({variant:i,size:a,className:t})),...c})}},757:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>v});var n=i(5155),s=i(2115);let a=(0,i(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var r=i(6766);function l(e){let{eventName:t,stadium:i,location:s,date:l,time:o,section:c,row:d,seats:h,ticketPrice:u,quantity:x,onConfirm:f,onClose:m}=e;return(0,n.jsx)("div",{className:"fixed inset-0 flex items-center justify-center bg-black/50 z-50",children:(0,n.jsxs)("div",{className:" bg-white rounded-lg shadow-lg w-[450px]",children:[(0,n.jsxs)("div",{className:"relative w-full h-[200px] bg-gray-200 rounded-t-lg overflow-hidden",children:[(0,n.jsx)(r.default,{src:"https://images.unsplash.com/photo-1558465202-92356bf74344?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",alt:"Event",width:450,height:200,className:"w-full h-full object-cover object-center"}),(0,n.jsx)(a,{color:"#000",className:"absolute top-4 right-4 cursor-pointer hover:bg-gray-100 rounded-full",onClick:m})]}),(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-center text-[#1D1D1D] mb-2",children:t}),(0,n.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-4",children:[(0,n.jsx)("p",{children:i}),(0,n.jsx)("p",{children:s})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-4",children:[(0,n.jsx)("p",{children:l}),(0,n.jsx)("p",{children:o})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Section ",c]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Row ",d]})]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Seats ",h]})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Ticket price: ",(0,n.jsxs)("span",{className:"text-[#02471F] font-bold",children:["$",u," each"]})]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Quantity: ",(0,n.jsxs)("span",{className:"font-bold",children:[x," tickets"]})]})]}),(0,n.jsx)("button",{onClick:f,className:"w-full bg-[#2ECC71] text-white font-bold py-2 rounded-lg hover:bg-[#28a65c] transition",children:"Confirm"})]})]})})}let o=(e,t,i)=>{let n=[];for(let s=0;s<t;s++){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZ"[s];for(let s=1;s<=i;s++){let i=.1>Math.random()?"sold":.1>Math.random()?"pending":"available";n.push({seatId:"".concat(e,"-").concat(t,"-").concat(s),sectionId:e,seatNumber:s.toString(),rowNumber:t,seatInRow:s,status:i})}}return n},c={venueId:"venue-1",name:"America First Field",address:"123 Main St",city:"Sandy, Utah, USA",ownerUserId:"user-1",createdAt:"2025-04-01T10:00:00Z",updatedAt:"2025-04-10T12:00:00Z",sections:[{sectionId:"section-1",venueId:"venue-1",name:"1",price:50,x:70,y:0,width:80,height:160,capacity:112,seats:o("section-1",14,8)},{sectionId:"section-2",name:"2",venueId:"venue-1",price:50,x:160,y:0,width:80,height:160,capacity:112,seats:o("section-2",14,8)},{sectionId:"section-3",name:"3",venueId:"venue-1",price:50,x:250,y:0,width:80,height:160,capacity:112,seats:o("section-3",14,8)},{sectionId:"section-4",name:"4",venueId:"venue-1",price:60,x:340,y:0,width:80,height:160,capacity:112,seats:o("section-4",14,8)},{sectionId:"section-5",name:"5",venueId:"venue-1",price:70,x:430,y:0,width:80,height:160,capacity:112,seats:o("section-5",14,8)},{sectionId:"section-6",name:"6",venueId:"venue-1",price:75,x:600,y:180,width:80,height:160,rotation:90,capacity:112,seats:o("section-6",14,8)},{sectionId:"section-7",name:"7",venueId:"venue-1",price:80,x:600,y:280,width:80,height:160,rotation:90,capacity:112,seats:o("section-7",14,8)},{sectionId:"section-8",name:"8",venueId:"venue-1",price:80,x:600,y:380,width:80,height:160,rotation:90,capacity:112,seats:o("section-8",14,8)},{sectionId:"section-9",name:"9",venueId:"venue-1",price:50,x:70,y:570,width:80,height:160,capacity:112,seats:o("section-9",14,8)},{sectionId:"section-10",name:"10",venueId:"venue-1",price:50,x:160,y:570,width:80,height:160,capacity:112,seats:o("section-10",14,8)},{sectionId:"section-11",name:"11",venueId:"venue-1",price:50,x:250,y:570,width:80,height:160,capacity:112,seats:o("section-11",14,8)},{sectionId:"section-12",name:"12",venueId:"venue-1",price:60,x:340,y:570,width:80,height:160,capacity:112,seats:o("section-12",14,8)},{sectionId:"section-13",name:"13",venueId:"venue-1",price:70,x:430,y:570,width:80,height:160,capacity:112,seats:o("section-13",14,8)},{sectionId:"section-14",name:"14",venueId:"venue-1",price:75,x:-100,y:180,width:80,height:160,rotation:90,capacity:112,seats:o("section-14",14,8)},{sectionId:"section-15",name:"15",venueId:"venue-1",price:80,x:-100,y:280,width:80,height:160,rotation:90,capacity:112,seats:o("section-15",14,8)},{sectionId:"section-16",name:"16",venueId:"venue-1",price:80,x:-100,y:380,width:80,height:160,rotation:90,capacity:112,seats:o("section-16",14,8)},{sectionId:"section-17",name:"17",venueId:"venue-1",price:80,x:-80,y:30,width:120,height:160,rotation:315,capacity:112,seats:o("section-17",14,8)},{sectionId:"section-18",name:"18",venueId:"venue-1",price:75,x:580,y:30,width:120,height:160,rotation:45,capacity:112,seats:o("section-18",14,8)},{sectionId:"section-19",name:"19",venueId:"venue-1",price:75,x:580,y:530,width:120,height:160,rotation:135,capacity:112,seats:o("section-19",14,8)},{sectionId:"section-20",name:"20",venueId:"venue-1",price:80,x:-80,y:530,width:120,height:160,rotation:225,capacity:112,seats:o("section-20",14,8)}]};var d=i(285);let h=function(e){let{section:t,detailLevel:i,isSelected:a,selectedSeats:r,onSectionClick:l,onSeatSelect:o,scale:c}=e,d="#6dfe4a",[h,u]=(0,s.useState)(d),[x,f]=(0,s.useState)({});(0,s.useEffect)(()=>{let e=t.seats.length,i=t.seats.filter(e=>"sold"===e.status||"pending"===e.status).length;u(p(d,Math.max(.4,e>0?1-i/e:1)));let n={};Object.entries(t.seats.reduce((e,t)=>(e[t.rowNumber]||(e[t.rowNumber]=[]),e[t.rowNumber].push(t),e),{})).forEach(e=>{let[t,i]=e,s=i.length,a=i.filter(e=>"sold"===e.status||"pending"===e.status).length,r=Math.max(.4,s>0?1-a/s:1);n[t]=p(d,r)}),f(n)},[t]);let m=a?1:.9,g=(a?2:1)/c,p=(e,t)=>{let i=parseInt(e.slice(1,3),16),n=parseInt(e.slice(3,5),16),s=parseInt(e.slice(5,7),16),a=Math.floor(i*t),r=Math.floor(n*t),l=Math.floor(s*t);return"#".concat(a.toString(16).padStart(2,"0")).concat(r.toString(16).padStart(2,"0")).concat(l.toString(16).padStart(2,"0"))};if("section"===i){var v;return(0,n.jsxs)("g",{transform:"translate(".concat(t.x,", ").concat(t.y,") ").concat(t.rotation?"rotate(".concat(t.rotation,")"):""),onClick:l,style:{cursor:"pointer"},children:[(0,n.jsx)("rect",{x:-(t.width||40)/2,y:-(t.height||40)/2,width:t.width,height:t.height,fill:h,opacity:m,stroke:"#000",strokeWidth:g,rx:2,ry:2}),(0,n.jsx)("g",{transform:"".concat(t.rotation?"rotate(".concat(-t.rotation,")"):""),children:(0,n.jsx)("text",{x:0,y:0,textAnchor:"middle",dominantBaseline:"middle",fontSize:12/c,fontWeight:"bold",fill:"#fff",children:t.name})}),(0,n.jsxs)("g",{transform:"translate(0, ".concat((null!=(v=t.height)?v:40)/2+10,") ").concat(t.rotation?"rotate(".concat(-t.rotation,")"):""),children:[(0,n.jsx)("rect",{x:-20/c,y:-10/c,width:40/c,height:20/c,rx:5/c,fill:"black",stroke:"#000",strokeWidth:.5/c}),(0,n.jsxs)("text",{textAnchor:"middle",dominantBaseline:"middle",fontSize:10/c,fontWeight:"bold",fill:"white",children:["$",t.price]})]})]})}if("row"===i){let e=Object.keys(t.seats.reduce((e,t)=>(e[t.rowNumber]||(e[t.rowNumber]=[]),e[t.rowNumber].push(t),e),{})).sort(),i=(t.height||100)/e.length;return(0,n.jsxs)("g",{transform:"translate(".concat(t.x,", ").concat(t.y,") ").concat(t.rotation?"rotate(".concat(t.rotation,")"):""),children:[(0,n.jsx)("rect",{x:-(t.width||100)/2,y:-(t.height||100)/2,width:t.width||100,height:t.height||100,fill:h,opacity:.3,stroke:"#000",strokeWidth:g,rx:2,ry:2}),e.map((e,s)=>{let a=-(t.height||100)/2+s*i,r=x[e]||h;return(0,n.jsxs)("g",{onClick:l,style:{cursor:"pointer"},children:[(0,n.jsx)("rect",{x:-(t.width||100)/2,y:a,width:t.width||100,height:i,fill:r,opacity:m,stroke:"#000",strokeWidth:.5/c}),(0,n.jsxs)("text",{x:0,y:a+i/2,textAnchor:"middle",dominantBaseline:"middle",fontSize:12/c,fill:"#fff",children:["Row ",e]})]},e)}),(0,n.jsx)("g",{transform:"".concat(t.rotation?"rotate(".concat(-t.rotation,")"):""),children:(0,n.jsx)("text",{x:0,y:-(t.height||100)/2-10,textAnchor:"middle",dominantBaseline:"middle",fontSize:14/c,fontWeight:"bold",fill:"#000",children:t.name})}),(0,n.jsxs)("g",{transform:"translate(0, ".concat((t.height||100)/2+15,") ").concat(t.rotation?"rotate(".concat(-t.rotation,")"):""),children:[(0,n.jsx)("rect",{x:-20/c,y:-10/c,width:40/c,height:20/c,rx:5/c,fill:"black",stroke:"#000",strokeWidth:.5/c}),(0,n.jsxs)("text",{textAnchor:"middle",dominantBaseline:"middle",fontSize:10/c,fontWeight:"bold",fill:"white",children:["$",t.price]})]})]})}if("seat"===i){let e=t.seats.reduce((e,t)=>(e[t.rowNumber]||(e[t.rowNumber]=[]),e[t.rowNumber].push(t),e),{}),i=Object.keys(e).sort(),s=(t.height||100)/i.length;return(0,n.jsxs)("g",{transform:"translate(".concat(t.x,", ").concat(t.y,") ").concat(t.rotation?"rotate(".concat(t.rotation,")"):""),children:[(0,n.jsx)("rect",{x:-(t.width||100)/2,y:-(t.height||100)/2,width:t.width||100,height:t.height||100,fill:h,opacity:.3,stroke:"#000",strokeWidth:g,rx:2,ry:2}),(0,n.jsx)("g",{transform:"".concat(t.rotation?"rotate(".concat(-t.rotation,")"):""),children:(0,n.jsx)("text",{x:0,y:-(t.height||100)/2-10,textAnchor:"middle",dominantBaseline:"middle",fontSize:16/c,fontWeight:"bold",fill:"#000",children:t.name})}),(0,n.jsxs)("g",{transform:"translate(0, ".concat((t.height||100)/2+15,") ").concat(t.rotation?"rotate(".concat(-t.rotation,")"):""),children:[(0,n.jsx)("rect",{x:-20/c,y:-10/c,width:40/c,height:20/c,rx:5/c,fill:"black",stroke:"#000",strokeWidth:.5/c}),(0,n.jsxs)("text",{textAnchor:"middle",dominantBaseline:"middle",fontSize:10/c,fontWeight:"bold",fill:"white",children:["$",t.price]})]}),i.map((i,a)=>{let l=-(t.height||100)/2+a*s,d=e[i].sort((e,t)=>e.seatInRow-t.seatInRow),h=(t.width||100)/d.length,u=Math.min(.8*h,.8*s)/2;return(0,n.jsxs)("g",{children:[(0,n.jsx)("text",{x:-(t.width||100)/2-10,y:l+s/2,textAnchor:"middle",dominantBaseline:"middle",fontSize:12/c,fontWeight:"bold",fill:"#000",children:i}),d.map((e,i)=>{let a="#ccc";"available"===e.status?a="#4CAF50":"sold"===e.status?a="#F44336":"pending"===e.status&&(a="#FFC107");let d=r.includes(e.seatId);d&&(a="#2196F3");let x=-(t.width||100)/2+i*h+h/2;return(0,n.jsxs)("g",{transform:"translate(".concat(x,", ").concat(l+s/2,")"),onClick:()=>{("available"===e.status||d)&&o(e.seatId)},style:{cursor:"available"===e.status||d?"pointer":"not-allowed"},children:[(0,n.jsx)("circle",{r:u,fill:a,stroke:"#000",strokeWidth:.5/c,opacity:"sold"===e.status?.7:1}),(0,n.jsx)("text",{textAnchor:"middle",dominantBaseline:"middle",fontSize:10/c,fontWeight:"bold",fill:d||"available"===e.status?"#fff":"#000",children:e.seatNumber})]},e.seatId)})]},i)})]})}return null},u=function(e){let{venue:t,selectedSeats:i,onSeatSelect:a}=e,r=(0,s.useRef)(null),l=(0,s.useRef)(null),[o,c]=(0,s.useState)({width:800,height:600}),[u,x]=(0,s.useState)(1),[f,m]=(0,s.useState)({x:400,y:300}),[g,p]=(0,s.useState)(!1),[v,w]=(0,s.useState)({x:0,y:0}),[b,y]=(0,s.useState)("section"),[j,N]=(0,s.useState)(null);(0,s.useEffect)(()=>{let e=()=>{if(r.current){let{clientWidth:e,clientHeight:t}=r.current;c({width:e||800,height:t||600})}};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,s.useEffect)(()=>{u<1.5?y("section"):u<3?y("row"):y("seat")},[u]);let k=e=>{if(N(e),"section"===b){var t,i;x(2),m({x:o.width/2-(null!=(t=e.x)?t:0)*u,y:o.height/2-(null!=(i=e.y)?i:0)*u})}};return(0,s.useEffect)(()=>{m({x:o.width/2,y:o.height/2})},[o]),(0,n.jsxs)("div",{ref:r,className:"relative w-full h-full",children:[(0,n.jsxs)("div",{className:"absolute top-4 right-4 z-10 flex flex-col gap-2",children:[(0,n.jsx)(d.$,{onClick:()=>{x(Math.min(1.2*u,5))},className:"w-10 h-10 p-0 rounded-full bg-green-500 hover:bg-green-600 text-white font-bold",children:"+"}),(0,n.jsx)(d.$,{onClick:()=>{x(Math.max(.8*u,.5))},className:"w-10 h-10 p-0 rounded-full bg-green-500 hover:bg-green-600 text-white font-bold",children:"-"}),(0,n.jsx)(d.$,{onClick:()=>{x(1),m({x:o.width/2,y:o.height/2}),N(null),y("section")},className:"w-10 h-10 p-0 rounded-full bg-green-500 hover:bg-green-600 text-white font-bold",children:"↺"})]}),(0,n.jsx)("svg",{ref:l,width:o.width,height:o.height,viewBox:"0 0 ".concat(o.width," ").concat(o.height),onWheel:e=>{var t;e.preventDefault();let i=Math.min(Math.max(e.deltaY>0?.9*u:1.1*u,.5),5),n=null==(t=l.current)?void 0:t.getBoundingClientRect();if(!n)return;let s=e.clientX-n.left,a=e.clientY-n.top,r=s-(s-f.x)*(i/u),o=a-(a-f.y)*(i/u);x(i),m({x:r,y:o})},onMouseDown:e=>{p(!0),w({x:e.clientX,y:e.clientY})},onMouseMove:e=>{if(!g)return;let t=e.clientX-v.x,i=e.clientY-v.y;m({x:f.x+t,y:f.y+i}),w({x:e.clientX,y:e.clientY})},onMouseUp:()=>{p(!1)},onMouseLeave:()=>{p(!1)},className:"touch-none",style:{background:"#f8f9fa"},children:(0,n.jsxs)("g",{transform:"translate(".concat(f.x,", ").concat(f.y,") scale(").concat(u,")"),children:[(0,n.jsx)("rect",{x:0,y:100,width:500,height:367,fill:"#4CAF50"}),Array.from({length:10}).map((e,t)=>(0,n.jsx)("rect",{x:50*t,y:100,width:50,height:367,fill:t%2==0?"#43A047":"#4CAF50"},t)),(0,n.jsx)("rect",{x:50,y:150,width:400,height:267,fill:"none",stroke:"#fff",strokeWidth:2/u}),(0,n.jsxs)("g",{stroke:"#fff",strokeWidth:1/u,fill:"none",children:[(0,n.jsx)("line",{x1:250,y1:150,x2:250,y2:417}),(0,n.jsx)("circle",{cx:250,cy:283.5,r:45}),(0,n.jsx)("rect",{x:50,y:208,width:80,height:150}),(0,n.jsx)("rect",{x:370,y:208,width:80,height:150}),(0,n.jsx)("rect",{x:50,y:245,width:30,height:75}),(0,n.jsx)("rect",{x:420,y:245,width:30,height:75}),(0,n.jsx)("rect",{x:40,y:258,width:10,height:50}),(0,n.jsx)("rect",{x:450,y:258,width:10,height:50}),(0,n.jsx)("path",{d:"M 130 283.5 A 45 45 0 0 1 130 283.5"}),(0,n.jsx)("path",{d:"M 370 283.5 A 45 45 0 0 O 370 283.5"})]}),t.sections.map(e=>(0,n.jsx)(h,{section:e,detailLevel:b,isSelected:(null==j?void 0:j.sectionId)===e.sectionId,selectedSeats:i,onSectionClick:()=>k(e),onSeatSelect:a,scale:u},e.sectionId))]})})]})},x=function(e){let{onSeatSelect:t,selectedSeats:i}=e,[a,r]=(0,s.useState)([]),l=i?i.map(e=>e.SeatId):a;return(0,n.jsx)("div",{className:"w-full h-full bg-white rounded-md shadow-md overflow-hidden",children:(0,n.jsx)(u,{venue:c,selectedSeats:l,onSeatSelect:e=>{if(t)for(let i of c.sections){let n=i.seats.find(t=>t.seatId===e);if(n)return void t({SeatId:n.seatId,SectionId:i.sectionId,SeatNumber:n.seatNumber,RowNumber:n.rowNumber,SeatInRow:parseInt(n.seatNumber)})}else r(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])}})})};var f=i(4627);function m(e){let{seatOrder:t,onRemove:i}=e;return(0,n.jsxs)("div",{className:"w-[90%] relative h-[80px] border-1 border-[#000000] rounded-[4px] flex flex-row",children:[(0,n.jsx)("div",{className:"aspect-[1/2] w-[24%] overflow-hidden rounded-[4px]",children:(0,n.jsx)(r.default,{src:f.A,alt:"homeBackground",className:"w-full h-full object-cover object-center rounded-[4px]"})}),(0,n.jsxs)("div",{className:"w-[75%] px-2 flex flex-col items-start justify-between",children:[(0,n.jsxs)("div",{className:"w-full flex flex-col",children:[(0,n.jsx)("p",{className:"text-[#1D1D1D] text-[16px] font-bold",children:t.SectionId}),(0,n.jsxs)("p",{className:"text-[#686868] text-[14px] font-medium",children:[t.RowNumber," | ",t.SeatNumber]})]}),(0,n.jsxs)("div",{className:"w-full flex flex-row items-center justify-between",children:[(0,n.jsx)("p",{className:"text-[#686868] text-[14px] font-medium",children:"Viewed By 9 People"}),(0,n.jsx)("p",{className:"text-[#02471F] text-[16px] font-bold",children:"$10"})]})]}),(0,n.jsx)("div",{className:"absolute top-1 right-1 w-[15px] h-[15px] rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-300",onClick:()=>i(t.SeatId),children:"x"})]})}var g=i(6783),p=i(5695);function v(){let{eventId:e}=(0,p.useParams)(),t=(0,p.useRouter)(),[i,a]=(0,s.useState)([]),[r,o]=(0,s.useState)(!1),{event:c,isLoadingEventDetails:h,errorEventDetails:u,loadEvent:f,clearDetails:v}=(0,g.mJ)();(0,s.useEffect)(()=>(e&&f(e),()=>{v()}),[e,f,v]);let w=e=>{a(t=>t.filter(t=>t.SeatId!==e))};return h?(0,n.jsx)("div",{className:"w-full h-[calc(100vh-80px)] flex items-center justify-center",children:(0,n.jsx)("div",{className:"text-lg",children:"Loading event details..."})}):u?(0,n.jsx)("div",{className:"w-full h-[calc(100vh-80px)] flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-lg text-red-500",children:["Error: ",u]})}):c?(0,n.jsxs)("div",{className:"w-full h-[calc(100vh-80px)] py-3 px-5 flex flex-row",children:[(0,n.jsxs)("div",{className:"w-[40%] h-full py-3 px-4 overflow-y-auto",children:[(0,n.jsxs)("div",{className:"bookingInfo w-full flex flex-col gap-3 border-b-1 border-[#D0D0D0] pb-3 mb-3",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-[#1D1D1D]",children:null==c?void 0:c.name}),(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("p",{className:"text-[#686868] text-[12px] font-medium",children:(null==c?void 0:c.startDateTime)?new Date(c.startDateTime).toLocaleString():"Start time not available"}),(0,n.jsx)("p",{className:"text-[#686868] text-[12px] font-medium",children:(null==c?void 0:c.endDateTime)?new Date(c.endDateTime).toLocaleString():"End time not available"})]})]}),(0,n.jsxs)("div",{className:"w-full flex flex-row justify-between mb-2",children:[(0,n.jsxs)("p",{className:"text-[#1D1D1D] text-[14px] font-semibold",children:[i.length," Selected Seats"]}),(0,n.jsxs)("p",{className:"text-[#02471F] text-[16px] font-bold",children:["Total price: $",10*i.length]})]}),(0,n.jsx)("div",{className:"selectedSeats flex flex-col items-center gap-3",children:i.map((e,t)=>(0,n.jsx)(m,{seatOrder:e,onRemove:w},t))})]}),(0,n.jsx)("div",{className:"w-[60%] h-full",children:(0,n.jsx)(x,{selectedSeats:i,onSeatSelect:e=>{a(t=>t.find(t=>t.SeatId===e.SeatId)?t.filter(t=>t.SeatId!==e.SeatId):[...t,e])}})}),r&&(0,n.jsx)(l,{eventName:"FC Barcelona vs Real Madrid",stadium:"My Dinh Stadium",location:"Ha Noi, Vietnam",date:"Mar 22 • Sat • 2025",time:"19:30 - 23:30",section:"99",row:"C",seats:"4-10",ticketPrice:10,quantity:7,onConfirm:()=>{},onClose:()=>o(!1)}),(0,n.jsx)(d.$,{className:"fixed bottom-4 left-1/6 py-3 px-8 text-xl font-bold rounded-4xl",onClick:()=>{t.push("/booking/payment/".concat(e))},children:"Book"})]}):(0,n.jsx)("div",{className:"w-full h-[calc(100vh-80px)] flex items-center justify-center",children:(0,n.jsx)("div",{className:"text-lg",children:"Event not found"})})}},1469:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return o},getImageProps:function(){return l}});let n=i(8229),s=i(8883),a=i(3063),r=n._(i(1193));function l(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let o=a.Image},2211:(e,t,i)=>{Promise.resolve().then(i.bind(i,757))},4627:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n={src:"/_next/static/media/Home_Background.20663903.png",height:720,width:1440,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAKlBMVEUZJSk4SEuZ0V1FTFUtPEImMjaNzE+RrXRgij2GomqZtIF8hnk9U1hvgWZX8Dr5AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJ0lEQVR4nAXBhwEAIAwCMKC76v/vmgBB8jqAoJk5cKZ2ax46JSn7AwlSAJxkVFnPAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4}},5695:(e,t,i)=>{"use strict";var n=i(8999);i.o(n,"useParams")&&i.d(t,{useParams:function(){return n.useParams}}),i.o(n,"useRouter")&&i.d(t,{useRouter:function(){return n.useRouter}})},6707:(e,t,i)=>{"use strict";i.d(t,{cn:()=>a});var n=i(2596),s=i(9688);function a(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return(0,s.QP)((0,n.$)(t))}},6766:(e,t,i)=>{"use strict";i.d(t,{default:()=>s.a});var n=i(1469),s=i.n(n)},6783:(e,t,i)=>{"use strict";i.d(t,{mJ:()=>m,KC:()=>f,Qb:()=>g});var n=i(2115),s=i(2522);let a=e=>e.events.events,r=e=>e.events.currentEvent,l=e=>e.events.isLoadingList,o=e=>e.events.isLoadingDetails,c=e=>e.events.isLoadingMutation,d=e=>e.events.errorList,h=e=>e.events.errorDetails,u=e=>e.events.errorMutation;var x=i(8710);let f=()=>{let e=(0,x.j)(),t=(0,x.G)(a),i=(0,x.G)(l);return{events:t,isLoadingList:i,errorEventList:(0,x.G)(d),loadEvents:(0,n.useCallback)(()=>e((0,s.fw)()),[e])}},m=()=>{let e=(0,x.j)(),t=(0,x.G)(r),i=(0,x.G)(o),a=(0,x.G)(h);return{event:t,isLoadingEventDetails:i,errorEventDetails:a,loadEvent:(0,n.useCallback)(t=>e((0,s.vR)(t)),[e]),clearDetails:(0,n.useCallback)(()=>{e((0,s.HB)())},[e])}},g=()=>{let e=(0,x.j)(),t=(0,x.G)(c),i=(0,x.G)(u),a=(0,n.useCallback)(t=>e((0,s.lh)(t)).unwrap(),[e]),r=(0,n.useCallback)((t,i)=>e((0,s.qM)({eventId:t,eventData:i})).unwrap(),[e]),l=(0,n.useCallback)(t=>e((0,s.SX)(t)).unwrap(),[e]),o=(0,n.useCallback)((t,i)=>e((0,s.nK)({eventId:t,rescheduleData:i})).unwrap(),[e]),d=(0,n.useCallback)(t=>e((0,s.ls)(t)).unwrap(),[e]),h=(0,n.useCallback)(t=>e((0,s.TL)(t)).unwrap(),[e]),f=(0,n.useCallback)(t=>e((0,s.i$)(t)).unwrap(),[e]);return{isLoadingEventMuatation:t,errorEventMutation:i,createEvent:a,updateEvent:r,removeEvent:l,approveEvent:f,rescheduleEvent:o,postponeEvent:d,cancelEvent:h,submitEvent:(0,n.useCallback)(t=>e((0,s.P_)(t)).unwrap(),[e]),clearError:(0,n.useCallback)(()=>{e((0,s.b9)())},[e])}}},8710:(e,t,i)=>{"use strict";i.d(t,{G:()=>a,j:()=>s});var n=i(4540);let s=n.wA.withTypes(),a=n.d4.withTypes()},9946:(e,t,i)=>{"use strict";i.d(t,{A:()=>d});var n=i(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase()),r=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,t)=>{let{color:i="currentColor",size:s=24,strokeWidth:a=2,absoluteStrokeWidth:r,className:c="",children:d,iconNode:h,...u}=e;return(0,n.createElement)("svg",{ref:t,...o,width:s,height:s,stroke:i,strokeWidth:r?24*Number(a)/Number(s):a,className:l("lucide",c),...u},[...h.map(e=>{let[t,i]=e;return(0,n.createElement)(t,i)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let i=(0,n.forwardRef)((i,a)=>{let{className:o,...d}=i;return(0,n.createElement)(c,{ref:a,iconNode:t,className:l("lucide-".concat(s(r(e))),"lucide-".concat(e),o),...d})});return i.displayName=r(e),i}}},e=>{var t=t=>e(e.s=t);e.O(0,[13,63,196,522,441,684,358],()=>t(2211)),_N_E=e.O()}]);