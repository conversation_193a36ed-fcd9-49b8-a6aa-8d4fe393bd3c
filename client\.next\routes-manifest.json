{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/admin/payments/[id]", "regex": "^/admin/payments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/payments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/auth/[slug]", "regex": "^/auth/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/auth/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/booking/payment/[eventId]", "regex": "^/booking/payment/([^/]+?)(?:/)?$", "routeKeys": {"nxtPeventId": "nxtPeventId"}, "namedRegex": "^/booking/payment/(?<nxtPeventId>[^/]+?)(?:/)?$"}, {"page": "/booking/[eventId]", "regex": "^/booking/([^/]+?)(?:/)?$", "routeKeys": {"nxtPeventId": "nxtPeventId"}, "namedRegex": "^/booking/(?<nxtPeventId>[^/]+?)(?:/)?$"}, {"page": "/event-detail/[id]", "regex": "^/event\\-detail/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/event\\-detail/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/orders/[id]", "regex": "^/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/orders/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/organizer/event/[id]", "regex": "^/organizer/event/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/organizer/event/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin/payments", "regex": "^/admin/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/payments(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/orders", "regex": "^/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders(?:/)?$"}, {"page": "/organizer", "regex": "^/organizer(?:/)?$", "routeKeys": {}, "namedRegex": "^/organizer(?:/)?$"}, {"page": "/organizer/event", "regex": "^/organizer/event(?:/)?$", "routeKeys": {}, "namedRegex": "^/organizer/event(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}