import BaseService from "./BaseService";
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from "@/utils/errorHandler";
import { 
  ApiBooking, 
  CreateBookingRequest, 
  BookingResponse, 
  SeatStatus 
} from "@/types/api";

class BookingService extends BaseService {
  constructor() {
    super("/api/bookings", {
      enableAuth: true, // Booking management requires authentication
    });
  }

  async createBooking(bookingData: CreateBookingRequest): Promise<BookingResponse> {
    try {
      const response = await this.post<BookingResponse>("", bookingData);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Create booking");
    }
  }

  async getMyBookings(): Promise<ApiBooking[]> {
    try {
      const response = await this.get<ApiBooking[]>("/my");
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Fetch my bookings");
    }
  }

  async getSeatStatusForEvent(eventId: string): Promise<SeatStatus[]> {
    try {
      const response = await this.get<SeatStatus[]>(`/status/${eventId}`);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Fetch seat status for event (${eventId})`);
    }
  }
}

const bookingService = new BookingService();
export default bookingService;
