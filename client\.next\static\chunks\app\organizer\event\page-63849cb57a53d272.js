(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[35],{2177:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(5155),u=s(2115),n=s(5695);function a(){let e=(0,n.useRouter)();return(0,u.useEffect)(()=>{e.push("/organizer/event/new")},[e]),(0,t.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,t.jsx)("p",{children:"Redirecting to event creation page..."})})}},4776:(e,r,s)=>{Promise.resolve().then(s.bind(s,2177))},5695:(e,r,s)=>{"use strict";var t=s(8999);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}})}},e=>{var r=r=>e(e.s=r);e.O(0,[441,684,358],()=>r(4776)),_N_E=e.O()}]);