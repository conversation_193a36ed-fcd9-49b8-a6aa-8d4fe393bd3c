"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{4254:(e,t,a)=>{a.d(t,{Ay:()=>v,Lx:()=>i,w2:()=>l,Ly:()=>g,E_:()=>u});var n=a(3464),r=a(5809),o=a(9509);class c{async login(e){try{return(await n.A.post("".concat(this.API_URL,"/auth/login"),e)).data}catch(e){r.zc.handleServiceErrorFromCatch(e,"Login")}}async signup(e){try{return(await n.A.post("".concat(this.API_URL,"/auth/register"),e)).data}catch(e){r.zc.handleServiceErrorFromCatch(e,"Signup")}}async forgotPassword(e){try{return(await n.A.post("".concat(this.API_URL,"/auth/forgot-password"),{email:e})).data}catch(e){r.zc.handleServiceErrorFromCatch(e,"Forgot password")}}constructor(){this.API_URL=o.env.NEXT_PUBLIC_BACKEND_API_URL||"http://localhost:8082/api"}}let s=new c;var d=a(1990);let i=(0,d.zD)("user/login",async(e,t)=>{let{rejectWithValue:a}=t;try{return await s.login(e)}catch(e){return a(r.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,d.zD)("auth/signup",async(e,t)=>{let{rejectWithValue:a}=t;try{return await s.signup(e)}catch(e){return a(r.zc.handleAsyncThunkErrorFromCatch(e))}}),l=(0,d.zD)("auth/forgotPassword",async(e,t)=>{let{rejectWithValue:a}=t;try{return await s.forgotPassword(e)}catch(e){return a(r.zc.handleAsyncThunkErrorFromCatch(e))}}),h=(0,d.Z0)({name:"user",initialState:{user:null,isLoading:!1,error:null,isAuthenticated:!1,forgotPasswordStatus:"idle",forgotPasswordError:null},reducers:{logout:e=>{e.user=null,e.isAuthenticated=!1,e.error=null},resetForgotPasswordState:e=>{e.forgotPasswordStatus="idle",e.forgotPasswordError=null}},extraReducers:e=>{e.addCase(i.pending,e=>{e.isLoading=!0,e.error=null}).addCase(i.fulfilled,(e,t)=>{e.isLoading=!1,e.user=t.payload,e.isAuthenticated=!0,e.error=null}).addCase(i.rejected,(e,t)=>{e.isLoading=!1,e.user=null,e.isAuthenticated=!1,e.error=t.payload||"Login failed"}).addCase(u.pending,e=>{e.isLoading=!0,e.error=null}).addCase(u.fulfilled,(e,t)=>{e.isLoading=!1,e.error=null,e.user=t.payload}).addCase(u.rejected,(e,t)=>{e.isLoading=!1,e.error=t.payload||"Signup failed"}).addCase(l.pending,e=>{e.forgotPasswordStatus="loading",e.forgotPasswordError=null}).addCase(l.fulfilled,e=>{e.forgotPasswordStatus="succeeded",e.forgotPasswordError=null}).addCase(l.rejected,(e,t)=>{e.forgotPasswordStatus="failed",e.forgotPasswordError=t.payload||"Failed to send reset link"})}}),{logout:y,resetForgotPasswordState:g}=h.actions,v=h.reducer},7602:(e,t,a)=>{a.d(t,{Mo:()=>f,Ay:()=>w,Of:()=>i,rh:()=>g,qT:()=>u});var n=a(1990),r=a(3464),o=a(5809),c=a(9509);class s{async getAllVenues(){try{return(await r.A.get("".concat(this.API_URL,"/Venues"))).data}catch(e){o.zc.handleServiceErrorFromCatch(e,"Fetch venues")}}async getVenueById(e){try{return(await r.A.get("".concat(this.API_URL,"/Venues/").concat(e))).data}catch(e){o.zc.handleServiceErrorFromCatch(e,"Fetch venue by ID")}}async createVenue(e){try{return(await r.A.post("".concat(this.API_URL,"/Venues"),e)).data}catch(e){o.zc.handleServiceErrorFromCatch(e,"Create venue")}}async updateVenue(e,t){try{return(await r.A.put("".concat(this.API_URL,"/Venues/").concat(e),t)).data}catch(t){o.zc.handleServiceErrorFromCatch(t,"Update venue (".concat(e,")"))}}async deleteVenue(e){try{await r.A.delete("".concat(this.API_URL,"/Venues/").concat(e))}catch(t){o.zc.handleServiceErrorFromCatch(t,"Delete venue (".concat(e,")"))}}async getAllSectionsForVenue(e){try{return(await r.A.get("".concat(this.API_URL,"/venues/").concat(e,"/sections"))).data}catch(t){o.zc.handleServiceErrorFromCatch(t,"Fetch sections for venue (".concat(e,")"))}}async getSectionById(e,t){try{return(await r.A.get("".concat(this.API_URL,"/venues/").concat(e,"/sections/").concat(t))).data}catch(a){o.zc.handleServiceErrorFromCatch(a,"Fetch section (".concat(t,") for venue (").concat(e,")"))}}async createSection(e,t){try{return(await r.A.post("".concat(this.API_URL,"/venues/").concat(e,"/sections"),t)).data}catch(t){o.zc.handleServiceErrorFromCatch(t,"Create section for venue (".concat(e,")"))}}async updateSection(e,t,a){try{return(await r.A.put("".concat(this.API_URL,"/venues/").concat(e,"/sections/").concat(t),a)).data}catch(a){o.zc.handleServiceErrorFromCatch(a,"Update section (".concat(t,") for venue (").concat(e,")"))}}async deleteSection(e,t){try{await r.A.delete("".concat(this.API_URL,"/venues/").concat(e,"/sections/").concat(t))}catch(a){o.zc.handleServiceErrorFromCatch(a,"Delete section (".concat(t,") for venue (").concat(e,")"))}}constructor(){this.API_URL=c.env.NEXT_PUBLIC_BACKEND_API_URL||"http://localhost:8083/api"}}let d=new s,i=(0,n.zD)("venues/fetchAll",async(e,t)=>{let{rejectWithValue:a}=t;try{return await d.getAllVenues()}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,n.zD)("venues/fetchById",async(e,t)=>{let{rejectWithValue:a}=t;try{return await d.getVenueById(e)}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),l=(0,n.zD)("venues/create",async(e,t)=>{let{rejectWithValue:a}=t;try{return await d.createVenue(e)}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),h=(0,n.zD)("venues/update",async(e,t)=>{let{venueId:a,venueData:n}=e,{rejectWithValue:r}=t;try{return await d.updateVenue(a,n)}catch(e){return r(o.zc.handleAsyncThunkErrorFromCatch(e))}}),y=(0,n.zD)("venues/delete",async(e,t)=>{let{rejectWithValue:a}=t;try{return await d.deleteVenue(e),e}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),g=(0,n.zD)("venues/sections/fetchAll",async(e,t)=>{let{rejectWithValue:a}=t;try{let t=await d.getAllSectionsForVenue(e);return{venueId:e,sections:t}}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),v=(0,n.zD)("venues/sections/create",async(e,t)=>{let{venueId:a,sectionData:n}=e,{rejectWithValue:r}=t;try{return await d.createSection(a,n)}catch(e){return r(o.zc.handleAsyncThunkErrorFromCatch(e))}}),p=(0,n.zD)("venues/sections/update",async(e,t)=>{let{venueId:a,sectionId:n,sectionData:r}=e,{rejectWithValue:c}=t;try{return await d.updateSection(a,n,r)}catch(e){return c(o.zc.handleAsyncThunkErrorFromCatch(e))}}),C=(0,n.zD)("venues/sections/delete",async(e,t)=>{let{venueId:a,sectionId:n}=e,{rejectWithValue:r}=t;try{return await d.deleteSection(a,n),{venueId:a,sectionId:n}}catch(e){return r(o.zc.handleAsyncThunkErrorFromCatch(e))}}),L=(0,n.Z0)({name:"venues",initialState:{venues:[],currentVenue:null,isLoadingList:!1,isLoadingDetails:!1,isLoadingSections:!1,isLoadingMutation:!1,errorList:null,errorDetails:null,errorSections:null,errorMutation:null},reducers:{clearCurrentVenue:e=>{e.currentVenue=null,e.errorDetails=null,e.errorSections=null},clearVenueMutationError:e=>{e.errorMutation=null}},extraReducers:e=>{e.addCase(i.pending,e=>{e.isLoadingList=!0,e.errorList=null}).addCase(i.fulfilled,(e,t)=>{e.isLoadingList=!1,e.venues=t.payload}).addCase(i.rejected,(e,t)=>{e.isLoadingList=!1,e.errorList=t.payload}).addCase(u.pending,e=>{e.isLoadingDetails=!0,e.errorDetails=null,e.currentVenue=null}).addCase(u.fulfilled,(e,t)=>{e.isLoadingDetails=!1,e.currentVenue=t.payload,e.currentVenue&&void 0===e.currentVenue.sections&&g(e.currentVenue.venueId)}).addCase(u.rejected,(e,t)=>{e.isLoadingDetails=!1,e.errorDetails=t.payload}).addCase(l.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(l.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.venues.push(t.payload)}).addCase(l.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(h.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(h.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let n=e.venues.findIndex(e=>e.venueId===t.payload.venueId);-1!==n&&(e.venues[n]=t.payload),(null==(a=e.currentVenue)?void 0:a.venueId)===t.payload.venueId&&(e.currentVenue=t.payload)}).addCase(h.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(y.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(y.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1,e.venues=e.venues.filter(e=>e.venueId!==t.payload),(null==(a=e.currentVenue)?void 0:a.venueId)===t.payload&&(e.currentVenue=null)}).addCase(y.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(g.pending,e=>{e.isLoadingSections=!0,e.errorSections=null}).addCase(g.fulfilled,(e,t)=>{var a;e.isLoadingSections=!1,(null==(a=e.currentVenue)?void 0:a.venueId)===t.payload.venueId&&(e.currentVenue.sections=t.payload.sections);let n=e.venues.find(e=>e.venueId===t.payload.venueId);n&&(n.sections=t.payload.sections)}).addCase(g.rejected,(e,t)=>{e.isLoadingSections=!1,e.errorSections=t.payload}).addCase(v.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(v.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1,(null==(a=e.currentVenue)?void 0:a.venueId)===t.payload.venueId&&(e.currentVenue.sections=[...e.currentVenue.sections||[],t.payload]);let n=e.venues.find(e=>e.venueId===t.payload.venueId);n&&(n.sections=[...n.sections||[],t.payload])}).addCase(v.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(p.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(p.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let{venueId:n,sectionId:r}=t.payload,o=e=>null==e?void 0:e.map(e=>e.sectionId===r?t.payload:e);(null==(a=e.currentVenue)?void 0:a.venueId)===n&&(e.currentVenue.sections=o(e.currentVenue.sections));let c=e.venues.find(e=>e.venueId===n);c&&(c.sections=o(c.sections))}).addCase(p.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(C.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(C.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let{venueId:n,sectionId:r}=t.payload,o=e=>(null==e?void 0:e.filter(e=>e.sectionId!==r))||[];(null==(a=e.currentVenue)?void 0:a.venueId)===n&&(e.currentVenue.sections=o(e.currentVenue.sections));let c=e.venues.find(e=>e.venueId===n);c&&(c.sections=o(c.sections))}).addCase(C.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload})}}),{clearCurrentVenue:f,clearVenueMutationError:A}=L.actions,w=L.reducer}}]);