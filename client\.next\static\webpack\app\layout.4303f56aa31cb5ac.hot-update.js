"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js":
/*!****************************************************!*\
  !*** ./node_modules/jwt-decode/build/esm/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidTokenError: () => (/* binding */ InvalidTokenError),\n/* harmony export */   jwtDecode: () => (/* binding */ jwtDecode)\n/* harmony export */ });\nclass InvalidTokenError extends Error {\n}\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n        return \"%\" + code;\n    }));\n}\nfunction base64UrlDecode(str) {\n    let output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"base64 string is not of the correct length\");\n    }\n    try {\n        return b64DecodeUnicode(output);\n    }\n    catch (err) {\n        return atob(output);\n    }\n}\nfunction jwtDecode(token, options) {\n    if (typeof token !== \"string\") {\n        throw new InvalidTokenError(\"Invalid token specified: must be a string\");\n    }\n    options || (options = {});\n    const pos = options.header === true ? 0 : 1;\n    const part = token.split(\".\")[pos];\n    if (typeof part !== \"string\") {\n        throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);\n    }\n    let decoded;\n    try {\n        decoded = base64UrlDecode(part);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);\n    }\n    try {\n        return JSON.parse(decoded);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9qd3QtZGVjb2RlL2J1aWxkL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsOEVBQThFLFFBQVE7QUFDdEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUZBQXlGLFNBQVMsR0FBRyxVQUFVO0FBQy9HO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1RkFBdUYsU0FBUyxHQUFHLFVBQVU7QUFDN0c7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFN0dWR5XFxDbGFzc2VzXFw2dGhTZW1lc3RlclxcU29mdHdhcmVBcmNoaXRlY3R1cmVcXHByb2plY3RcXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxqd3QtZGVjb2RlXFxidWlsZFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgSW52YWxpZFRva2VuRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG59XG5JbnZhbGlkVG9rZW5FcnJvci5wcm90b3R5cGUubmFtZSA9IFwiSW52YWxpZFRva2VuRXJyb3JcIjtcbmZ1bmN0aW9uIGI2NERlY29kZVVuaWNvZGUoc3RyKSB7XG4gICAgcmV0dXJuIGRlY29kZVVSSUNvbXBvbmVudChhdG9iKHN0cikucmVwbGFjZSgvKC4pL2csIChtLCBwKSA9PiB7XG4gICAgICAgIGxldCBjb2RlID0gcC5jaGFyQ29kZUF0KDApLnRvU3RyaW5nKDE2KS50b1VwcGVyQ2FzZSgpO1xuICAgICAgICBpZiAoY29kZS5sZW5ndGggPCAyKSB7XG4gICAgICAgICAgICBjb2RlID0gXCIwXCIgKyBjb2RlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBcIiVcIiArIGNvZGU7XG4gICAgfSkpO1xufVxuZnVuY3Rpb24gYmFzZTY0VXJsRGVjb2RlKHN0cikge1xuICAgIGxldCBvdXRwdXQgPSBzdHIucmVwbGFjZSgvLS9nLCBcIitcIikucmVwbGFjZSgvXy9nLCBcIi9cIik7XG4gICAgc3dpdGNoIChvdXRwdXQubGVuZ3RoICUgNCkge1xuICAgICAgICBjYXNlIDA6XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAyOlxuICAgICAgICAgICAgb3V0cHV0ICs9IFwiPT1cIjtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDM6XG4gICAgICAgICAgICBvdXRwdXQgKz0gXCI9XCI7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcImJhc2U2NCBzdHJpbmcgaXMgbm90IG9mIHRoZSBjb3JyZWN0IGxlbmd0aFwiKTtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIGI2NERlY29kZVVuaWNvZGUob3V0cHV0KTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycikge1xuICAgICAgICByZXR1cm4gYXRvYihvdXRwdXQpO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBqd3REZWNvZGUodG9rZW4sIG9wdGlvbnMpIHtcbiAgICBpZiAodHlwZW9mIHRva2VuICE9PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHRocm93IG5ldyBJbnZhbGlkVG9rZW5FcnJvcihcIkludmFsaWQgdG9rZW4gc3BlY2lmaWVkOiBtdXN0IGJlIGEgc3RyaW5nXCIpO1xuICAgIH1cbiAgICBvcHRpb25zIHx8IChvcHRpb25zID0ge30pO1xuICAgIGNvbnN0IHBvcyA9IG9wdGlvbnMuaGVhZGVyID09PSB0cnVlID8gMCA6IDE7XG4gICAgY29uc3QgcGFydCA9IHRva2VuLnNwbGl0KFwiLlwiKVtwb3NdO1xuICAgIGlmICh0eXBlb2YgcGFydCAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgICB0aHJvdyBuZXcgSW52YWxpZFRva2VuRXJyb3IoYEludmFsaWQgdG9rZW4gc3BlY2lmaWVkOiBtaXNzaW5nIHBhcnQgIyR7cG9zICsgMX1gKTtcbiAgICB9XG4gICAgbGV0IGRlY29kZWQ7XG4gICAgdHJ5IHtcbiAgICAgICAgZGVjb2RlZCA9IGJhc2U2NFVybERlY29kZShwYXJ0KTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgdGhyb3cgbmV3IEludmFsaWRUb2tlbkVycm9yKGBJbnZhbGlkIHRva2VuIHNwZWNpZmllZDogaW52YWxpZCBiYXNlNjQgZm9yIHBhcnQgIyR7cG9zICsgMX0gKCR7ZS5tZXNzYWdlfSlgKTtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIEpTT04ucGFyc2UoZGVjb2RlZCk7XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG4gICAgICAgIHRocm93IG5ldyBJbnZhbGlkVG9rZW5FcnJvcihgSW52YWxpZCB0b2tlbiBzcGVjaWZpZWQ6IGludmFsaWQganNvbiBmb3IgcGFydCAjJHtwb3MgKyAxfSAoJHtlLm1lc3NhZ2V9KWApO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f6529013a1cc\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcU3R1ZHlcXENsYXNzZXNcXDZ0aFNlbWVzdGVyXFxTb2Z0d2FyZUFyY2hpdGVjdHVyZVxccHJvamVjdFxcY2xpZW50XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmNjUyOTAxM2ExY2NcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/constants/UserRole.ts":
/*!***********************************!*\
  !*** ./src/constants/UserRole.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* eslint-disable no-unused-vars */ var UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[UserRole[\"ADMIN\"] = 0] = \"ADMIN\";\n    UserRole[UserRole[\"USER\"] = 1] = \"USER\";\n    UserRole[UserRole[\"ORGANIZER\"] = 2] = \"ORGANIZER\";\n    return UserRole;\n}({});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserRole);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb25zdGFudHMvVXNlclJvbGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxpQ0FBaUMsR0FDMUIsc0NBQUtBOzs7O1dBQUFBO01BSVg7QUFFRCxpRUFBZUEsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXFN0dWR5XFxDbGFzc2VzXFw2dGhTZW1lc3RlclxcU29mdHdhcmVBcmNoaXRlY3R1cmVcXHByb2plY3RcXGNsaWVudFxcc3JjXFxjb25zdGFudHNcXFVzZXJSb2xlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIGVzbGludC1kaXNhYmxlIG5vLXVudXNlZC12YXJzICovXHJcbmV4cG9ydCBlbnVtIFVzZXJSb2xlIHtcclxuICBBRE1JTixcclxuICBVU0VSLFxyXG4gIE9SR0FOSVpFUixcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgVXNlclJvbGU7XHJcbiJdLCJuYW1lcyI6WyJVc2VyUm9sZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/constants/UserRole.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/constants/index.ts":
/*!********************************!*\
  !*** ./src/constants/index.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserRole: () => (/* reexport safe */ _UserRole__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _UserRole__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UserRole */ \"(app-pages-browser)/./src/constants/UserRole.ts\");\n// directory for constants\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb25zdGFudHMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwwQkFBMEI7QUFDUTtBQUNkIiwic291cmNlcyI6WyJEOlxcU3R1ZHlcXENsYXNzZXNcXDZ0aFNlbWVzdGVyXFxTb2Z0d2FyZUFyY2hpdGVjdHVyZVxccHJvamVjdFxcY2xpZW50XFxzcmNcXGNvbnN0YW50c1xcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZGlyZWN0b3J5IGZvciBjb25zdGFudHNcclxuaW1wb3J0IFVzZXJSb2xlIGZyb20gXCIuL1VzZXJSb2xlXCI7XHJcbmV4cG9ydCB7IFVzZXJSb2xlIH07XHJcbiJdLCJuYW1lcyI6WyJVc2VyUm9sZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/constants/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/store/user/userSlice.ts":
/*!*************************************!*\
  !*** ./src/store/user/userSlice.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   requestPasswordReset: () => (/* binding */ requestPasswordReset),\n/* harmony export */   resetForgotPasswordState: () => (/* binding */ resetForgotPasswordState),\n/* harmony export */   signupUser: () => (/* binding */ signupUser)\n/* harmony export */ });\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/authService */ \"(app-pages-browser)/./src/services/authService.ts\");\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/errorHandler */ \"(app-pages-browser)/./src/utils/errorHandler.ts\");\n/* harmony import */ var _utils_jwtUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/jwtUtils */ \"(app-pages-browser)/./src/utils/jwtUtils.ts\");\n\n\n\n\nconst initialState = {\n    user: null,\n    isLoading: false,\n    error: null,\n    isAuthenticated: false,\n    forgotPasswordStatus: \"idle\",\n    forgotPasswordError: null\n};\nconst loginUser = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_3__.createAsyncThunk)(\"user/login\", async (credentials, param)=>{\n    let { rejectWithValue } = param;\n    try {\n        const loginResponse = await _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].login(credentials);\n        // Process the JWT token and return User data\n        const user = (0,_utils_jwtUtils__WEBPACK_IMPORTED_MODULE_2__.processLoginResponse)(loginResponse);\n        return user;\n    } catch (error) {\n        return rejectWithValue(_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.ErrorHandler.handleAsyncThunkErrorFromCatch(error));\n    }\n});\nconst signupUser = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_3__.createAsyncThunk)(\"auth/signup\", async (userData, param)=>{\n    let { rejectWithValue } = param;\n    try {\n        const data = await _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].signup(userData);\n        return data;\n    } catch (error) {\n        return rejectWithValue(_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.ErrorHandler.handleAsyncThunkErrorFromCatch(error));\n    }\n});\nconst requestPasswordReset = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_3__.createAsyncThunk)(\"auth/forgotPassword\", async (email, param)=>{\n    let { rejectWithValue } = param;\n    try {\n        const data = await _services_authService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].forgotPassword(email);\n        return data;\n    } catch (error) {\n        return rejectWithValue(_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.ErrorHandler.handleAsyncThunkErrorFromCatch(error));\n    }\n});\nconst userSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_3__.createSlice)({\n    name: \"user\",\n    initialState,\n    reducers: {\n        logout: (state)=>{\n            state.user = null;\n            state.isAuthenticated = false;\n            state.error = null;\n        // localStorage.removeItem('user'); // Clear local storage\n        },\n        // Reset forgot password state\n        resetForgotPasswordState: (state)=>{\n            state.forgotPasswordStatus = \"idle\";\n            state.forgotPasswordError = null;\n        }\n    },\n    extraReducers: (builder)=>{\n        builder// Handle login thunk\n        .addCase(loginUser.pending, (state)=>{\n            state.isLoading = true;\n            state.error = null;\n        }).addCase(loginUser.fulfilled, (state, action)=>{\n            state.isLoading = false;\n            // payload:\n            //         {\n            //     \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************.d0rKgpBUh8l_SDZRFhtFbjXbul4cd9k7y9IpvK7bdZ4\",\n            //     \"expiration\": \"2025-05-29T14:02:47.7232784Z\"\n            // } -> have to decode token, without secret\n            console.log(\"Login payload:\", action.payload);\n            state.user = action.payload;\n            state.isAuthenticated = true;\n            state.error = null;\n        }).addCase(loginUser.rejected, (state, action)=>{\n            state.isLoading = false;\n            state.user = null;\n            state.isAuthenticated = false;\n            state.error = action.payload || \"Login failed\";\n        }).addCase(signupUser.pending, (state)=>{\n            state.isLoading = true;\n            state.error = null;\n        }).addCase(signupUser.fulfilled, (state, action)=>{\n            state.isLoading = false;\n            state.error = null; // Clear any previous errors\n            state.user = action.payload;\n        }).addCase(signupUser.rejected, (state, action)=>{\n            state.isLoading = false;\n            state.error = action.payload || \"Signup failed\";\n        })// Handle forgot password thunk\n        .addCase(requestPasswordReset.pending, (state)=>{\n            state.forgotPasswordStatus = \"loading\";\n            state.forgotPasswordError = null;\n        }).addCase(requestPasswordReset.fulfilled, (state)=>{\n            state.forgotPasswordStatus = \"succeeded\";\n            state.forgotPasswordError = null;\n        }).addCase(requestPasswordReset.rejected, (state, action)=>{\n            state.forgotPasswordStatus = \"failed\";\n            state.forgotPasswordError = action.payload || \"Failed to send reset link\";\n        });\n    }\n});\nconst { logout, resetForgotPasswordState } = userSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (userSlice.reducer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/user/userSlice.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/jwtUtils.ts":
/*!*******************************!*\
  !*** ./src/utils/jwtUtils.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   decodeJWTToken: () => (/* binding */ decodeJWTToken),\n/* harmony export */   getStoredToken: () => (/* binding */ getStoredToken),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   jwtPayloadToUser: () => (/* binding */ jwtPayloadToUser),\n/* harmony export */   processLoginResponse: () => (/* binding */ processLoginResponse),\n/* harmony export */   storeAuthData: () => (/* binding */ storeAuthData)\n/* harmony export */ });\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.ts\");\n\n\n\n/**\n * Decode JWT token and extract user information\n */ function decodeJWTToken(token) {\n    try {\n        return (0,jwt_decode__WEBPACK_IMPORTED_MODULE_0__.jwtDecode)(token);\n    } catch (error) {\n        console.error('Failed to decode JWT token:', error);\n        throw new Error('Invalid JWT token');\n    }\n}\n/**\n * Convert JWT payload to User model\n */ function jwtPayloadToUser(payload) {\n    // Map role string to UserRole enum\n    const mapRole = (roleString)=>{\n        switch(roleString.toUpperCase()){\n            case 'USER':\n                return _constants__WEBPACK_IMPORTED_MODULE_2__.UserRole.USER;\n            case 'ORGANIZER':\n                return _constants__WEBPACK_IMPORTED_MODULE_2__.UserRole.ORGANIZER;\n            case 'ADMIN':\n                return _constants__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN;\n            default:\n                return _constants__WEBPACK_IMPORTED_MODULE_2__.UserRole.USER; // Default fallback\n        }\n    };\n    return {\n        userId: payload.sub,\n        userName: payload.email.split('@')[0],\n        email: payload.email,\n        firstName: '',\n        lastName: '',\n        role: mapRole(payload.role)\n    };\n}\n/**\n * Store authentication data in cookies\n */ function storeAuthData(loginResponse) {\n    const { token, expiration } = loginResponse;\n    // Calculate expiration date\n    const expirationDate = new Date(expiration);\n    // Store token in cookie with expiration\n    js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set('token', token, {\n        expires: expirationDate,\n        secure: \"development\" === 'production',\n        sameSite: 'strict'\n    });\n    console.log('Token stored in cookies:', {\n        token: token.substring(0, 20) + '...',\n        expiration: expirationDate.toISOString()\n    });\n}\n/**\n * Clear authentication data from cookies\n */ function clearAuthData() {\n    js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove('token');\n    console.log('Authentication data cleared from cookies');\n}\n/**\n * Get stored token from cookies\n */ function getStoredToken() {\n    return js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get('token');\n}\n/**\n * Check if token is expired\n */ function isTokenExpired(token) {\n    try {\n        const payload = decodeJWTToken(token);\n        const currentTime = Math.floor(Date.now() / 1000);\n        return payload.exp < currentTime;\n    } catch (e) {\n        return true; // If we can't decode, consider it expired\n    }\n}\n/**\n * Process login response and return user data\n */ function processLoginResponse(loginResponse) {\n    // Store token in cookies\n    storeAuthData(loginResponse);\n    // Decode token and extract user info\n    const payload = decodeJWTToken(loginResponse.token);\n    // Convert to User model\n    return jwtPayloadToUser(payload);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/jwtUtils.ts\n"));

/***/ })

});