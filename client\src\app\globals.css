@import "tailwindcss";

@theme inline {
  --color-background: #fdfdfd;
  --color-primary: #2ecc71;
  --color-secondary: #02471f;
  --color-darkText: #1d1d1d;
  --color-whiteText: #ffffff;
  --color-grayText: #686868;
  --color-darkStroke: #686868cc;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  background: #fdfdfd;
  color: #000000;
}

/* Rich Text Editor Styles - Simplified for better performance */
.prose {
  max-width: none;
}

.prose h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0.75rem 0 0.5rem;
}

.prose h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0.75rem 0 0.5rem;
}

.prose p {
  margin: 0.5rem 0;
}

.prose ul,
.prose ol {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose ul {
  list-style-type: disc;
}
.prose ol {
  list-style-type: decimal;
}

.prose blockquote {
  border-left: 3px solid #e5e7eb;
  padding-left: 1rem;
  font-style: italic;
  color: #4b5563;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: monospace;
}

.prose pre {
  background-color: #f3f4f6;
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow-x: auto;
}

/* Simple Rich Editor Placeholder */
[contenteditable]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
  display: block;
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateX(150px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
