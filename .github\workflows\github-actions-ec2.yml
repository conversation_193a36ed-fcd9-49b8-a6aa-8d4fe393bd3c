name: CI/CD Pipeline

on:
  push:
    branches:
      - main # Trigger on push to main branch
  pull_request:
    branches:
      - main # Trigger on pull requests to main branch

jobs:
  ci-cd:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./client

    steps:
      # CI Steps - Run for both push and pull requests
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: "./client/package-lock.json"

      - name: Install dependencies
        run: npm ci

      - name: Lint code
        run: npm run lint

      - name: Check formatting
        run: npm run format:check

      # - name: Run tests
      #   run: npm test

      - name: Build application
        run: npm run build

      # CD Steps - Only run on push to main branch
      - name: Set up Docker
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Set up Docker image tags
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKER_HUB_USERNAME }}/ticket-booking-client
          tags: |
            type=raw,value=latest
            type=sha,format=short

      - name: Build and push Docker image
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: docker/build-push-action@v4
        with:
          context: ./client
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Deploy to EC2
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        env:
          SSH_PRIVATE_KEY: ${{ secrets.EC2_SSH_KEY }}
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key.pem
          chmod 600 private_key.pem

          # Test SSH connection with verbose output
          echo "Testing SSH connection to $EC2_HOST..."
          ssh -v -i private_key.pem -o StrictHostKeyChecking=no -o ConnectTimeout=10 $EC2_USER@$EC2_HOST "echo SSH connection successful"

          if [ $? -ne 0 ]; then
            echo "SSH connection failed. Please check your EC2 instance security groups, network settings, and that the instance is running."
            exit 1
          fi

          # SSH into the EC2 instance and deploy
          ssh -i private_key.pem -o StrictHostKeyChecking=no -o ConnectTimeout=60 $EC2_USER@$EC2_HOST << EOF
            # Login to Docker Hub
            echo "${{ secrets.DOCKER_HUB_TOKEN }}" | docker login -u ${{ secrets.DOCKER_HUB_USERNAME }} --password-stdin

            # Pull the latest image
            docker pull ${{ secrets.DOCKER_HUB_USERNAME }}/ticket-booking-client:latest

            # Stop and remove existing container if it exists
            docker stop ticket-booking-container || true
            docker rm ticket-booking-container || true

            # Create a Docker network for the containers
            docker network create --driver=overlay --attachable ticket-app-network || true

            # Run the Next.js application container
            docker run -d --name ticket-booking-container \
              --network ticket-app-network \
              -e NODE_ENV=production \
              -e DEPLOY_VERSION=$(date +%Y%m%d-%H%M%S) \
              ${{ secrets.DOCKER_HUB_USERNAME }}/ticket-booking-client:latest

            # Create Nginx configuration
            mkdir -p /home/<USER>/nginx/conf.d

            # Create Nginx configuration with proper variable substitution
            DOMAIN="${{ secrets.DOMAIN_NAME }}"

            cat > /home/<USER>/nginx/conf.d/default.conf << EOF
            server {
                listen 80;
                server_name ${DOMAIN};

                location / {
                    return 301 https://\$host\$request_uri;
                }
            }

            server {
                listen 443 ssl;
                server_name ${DOMAIN};

                ssl_certificate /etc/nginx/ssl/live/${DOMAIN}/fullchain.pem;
                ssl_certificate_key /etc/nginx/ssl/live/${DOMAIN}/privkey.pem;

                location / {
                    proxy_pass http://ticket-booking-container:3000;
                    proxy_set_header Host \$host;
                    proxy_set_header X-Real-IP \$remote_addr;
                    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto \$scheme;
                }
            }
            EOF

            # Set up Certbot for SSL certificates
            if [ ! -d "/home/<USER>/certbot" ]; then
              mkdir -p /home/<USER>/certbot/conf /home/<USER>/certbot/www
            fi

            # Run Certbot container to obtain SSL certificates if they don't exist
            if [ ! -d "/home/<USER>/certbot/conf/live/${{ secrets.DOMAIN_NAME }}" ]; then
              docker run --rm \
                -v /home/<USER>/certbot/conf:/etc/letsencrypt \
                -v /home/<USER>/certbot/www:/var/www/certbot \
                certbot/certbot certonly --standalone \
                --agree-tos --no-eff-email \
                -m ${{ secrets.ADMIN_EMAIL }} \
                -d ${{ secrets.DOMAIN_NAME }}
            fi

            # Run Nginx container
            docker stop nginx-proxy || true
            docker rm nginx-proxy || true

            docker run -d --name nginx-proxy \
              --network ticket-app-network \
              -p 80:80 -p 443:443 \
              -v /home/<USER>/nginx/conf.d:/etc/nginx/conf.d \
              -v /home/<USER>/certbot/conf:/etc/nginx/ssl \
              -v /home/<USER>/certbot/www:/var/www/certbot \
              nginx:alpine

            # Clean up unused images
            docker image prune -f

            # Set up certificate renewal cron job
            echo "0 12 * * * docker run --rm -v /home/<USER>/certbot/conf:/etc/letsencrypt -v /home/<USER>/certbot/www:/var/www/certbot certbot/certbot renew --quiet && docker restart nginx-proxy" | crontab -

            # Logout from Docker Hub
            docker logout
          EOF
