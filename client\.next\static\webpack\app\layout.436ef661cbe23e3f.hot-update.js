"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d943f68e816a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcU3R1ZHlcXENsYXNzZXNcXDZ0aFNlbWVzdGVyXFxTb2Z0d2FyZUFyY2hpdGVjdHVyZVxccHJvamVjdFxcY2xpZW50XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkOTQzZjY4ZTgxNmFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/models/Event.ts":
/*!*****************************!*\
  !*** ./src/models/Event.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventCategory: () => (/* binding */ EventCategory),\n/* harmony export */   EventStatus: () => (/* binding */ EventStatus)\n/* harmony export */ });\n/* eslint-disable no-unused-vars */ var EventCategory = /*#__PURE__*/ function(EventCategory) {\n    EventCategory[\"CONCERT\"] = \"CONCERT\";\n    EventCategory[\"MATCH\"] = \"MATCH\";\n    EventCategory[\"OTHERS\"] = \"OTHERS\";\n    return EventCategory;\n}({});\nvar EventStatus = /*#__PURE__*/ function(EventStatus) {\n    EventStatus[\"DRAFT\"] = \"Draft\";\n    EventStatus[\"SUBMIT_FOR_APPROVAL\"] = \"Submit for approval\";\n    EventStatus[\"PUBLISHED\"] = \"Published\";\n    EventStatus[\"POSTPONED\"] = \"Postponed\";\n    EventStatus[\"RESCHEDULED\"] = \"Rescheduled\";\n    EventStatus[\"CANCELED\"] = \"Canceled\";\n    EventStatus[\"REJECTED\"] = \"Rejected\";\n    return EventStatus;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9tb2RlbHMvRXZlbnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUEwQkEsaUNBQWlDLEdBQzFCLDJDQUFLQTs7OztXQUFBQTtNQUlYO0FBRU0seUNBQUtDOzs7Ozs7OztXQUFBQTtNQVFYIiwic291cmNlcyI6WyJEOlxcU3R1ZHlcXENsYXNzZXNcXDZ0aFNlbWVzdGVyXFxTb2Z0d2FyZUFyY2hpdGVjdHVyZVxccHJvamVjdFxcY2xpZW50XFxzcmNcXG1vZGVsc1xcRXZlbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsidHlwZSBFdmVudCA9IHtcclxuICBldmVudElkOiBzdHJpbmc7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgY2F0ZWdvcnk6IEV2ZW50Q2F0ZWdvcnk7XHJcbiAgc3RhcnREYXRlVGltZTogRGF0ZTtcclxuICBlbmREYXRlVGltZTogRGF0ZTtcclxuICBzdGF0dXM6IEV2ZW50U3RhdHVzO1xyXG4gIHZlbnVlSWQ6IHN0cmluZztcclxuICB2ZW51ZU5hbWU6IHN0cmluZztcclxuICB2ZW51ZUFkZHJlc3M6IHN0cmluZztcclxuICBvcmdhbml6ZXJVc2VySWQ6IHN0cmluZztcclxuICBwb3N0ZXI6IHN0cmluZztcclxuICBpbWFnZXM6IHN0cmluZ1tdO1xyXG4gIGRldGFpbHM6IHN0cmluZztcclxuICBjcmVhdGVkQXQ6IERhdGU7XHJcbiAgdXBkYXRlZEF0OiBEYXRlO1xyXG4gIHNlY3Rpb25QcmljaW5nOiBFdmVudFNlY3Rpb25QcmljaW5nW107XHJcbn07XHJcblxyXG5leHBvcnQgdHlwZSBFdmVudFNlY3Rpb25QcmljaW5nID0ge1xyXG4gIGV2ZW50SWQ6IHN0cmluZztcclxuICBzZWN0aW9uSWQ6IHN0cmluZztcclxuICBwcmljZTogbnVtYmVyO1xyXG59O1xyXG5cclxuLyogZXNsaW50LWRpc2FibGUgbm8tdW51c2VkLXZhcnMgKi9cclxuZXhwb3J0IGVudW0gRXZlbnRDYXRlZ29yeSB7XHJcbiAgQ09OQ0VSVCA9IFwiQ09OQ0VSVFwiLFxyXG4gIE1BVENIID0gXCJNQVRDSFwiLFxyXG4gIE9USEVSUyA9IFwiT1RIRVJTXCIsXHJcbn1cclxuXHJcbmV4cG9ydCBlbnVtIEV2ZW50U3RhdHVzIHtcclxuICBEUkFGVCA9IFwiRHJhZnRcIixcclxuICBTVUJNSVRfRk9SX0FQUFJPVkFMID0gXCJTdWJtaXQgZm9yIGFwcHJvdmFsXCIsXHJcbiAgUFVCTElTSEVEID0gXCJQdWJsaXNoZWRcIixcclxuICBQT1NUUE9ORUQgPSBcIlBvc3Rwb25lZFwiLFxyXG4gIFJFU0NIRURVTEVEID0gXCJSZXNjaGVkdWxlZFwiLFxyXG4gIENBTkNFTEVEID0gXCJDYW5jZWxlZFwiLFxyXG4gIFJFSkVDVEVEID0gXCJSZWplY3RlZFwiLFxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBFdmVudDtcclxuIl0sIm5hbWVzIjpbIkV2ZW50Q2F0ZWdvcnkiLCJFdmVudFN0YXR1cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/models/Event.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/apiAdapters.ts":
/*!**********************************!*\
  !*** ./src/utils/apiAdapters.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adaptApiEventToEvent: () => (/* binding */ adaptApiEventToEvent),\n/* harmony export */   adaptApiUserToUser: () => (/* binding */ adaptApiUserToUser),\n/* harmony export */   adaptApiVenueToVenue: () => (/* binding */ adaptApiVenueToVenue),\n/* harmony export */   adaptAuthResponseToUser: () => (/* binding */ adaptAuthResponseToUser),\n/* harmony export */   adaptEventToCreateRequest: () => (/* binding */ adaptEventToCreateRequest),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   storeAuthData: () => (/* binding */ storeAuthData)\n/* harmony export */ });\n/* harmony import */ var _models_Event__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/Event */ \"(app-pages-browser)/./src/models/Event.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.ts\");\n// API Response Adapters\n// Convert backend API responses to frontend models\n\n\n/**\n * Convert AuthResponse to User model for Redux state\n * Note: AuthResponse doesn't contain full user data, so we create a minimal User object\n */ function adaptAuthResponseToUser(authResponse) {\n    return {\n        userId: authResponse.userId || \"\",\n        userName: \"\",\n        email: \"\",\n        firstName: \"\",\n        lastName: \"\",\n        role: _constants__WEBPACK_IMPORTED_MODULE_1__.UserRole.USER\n    };\n}\n/**\n * Convert ApiUser to User model\n */ function adaptApiUserToUser(apiUser) {\n    return {\n        userId: apiUser.id,\n        userName: apiUser.username,\n        email: apiUser.email,\n        firstName: apiUser.firstName,\n        lastName: apiUser.lastName,\n        role: _constants__WEBPACK_IMPORTED_MODULE_1__.UserRole.USER\n    };\n}\n/**\n * Convert ApiEvent to Event model with proper date parsing\n */ function adaptApiEventToEvent(apiEvent) {\n    // Combine date and time strings to create full DateTime\n    const startDateTime = new Date(\"\".concat(apiEvent.date, \"T\").concat(apiEvent.startTime));\n    const endDateTime = new Date(\"\".concat(apiEvent.date, \"T\").concat(apiEvent.endTime));\n    // Map backend status to frontend enum\n    const mapStatus = (backendStatus)=>{\n        switch(backendStatus){\n            case \"Draft\":\n                return _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.DRAFT;\n            case \"Pending\":\n                return _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.SUBMIT_FOR_APPROVAL;\n            case \"Approved\":\n                return _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.PUBLISHED;\n            case \"Cancelled\":\n                return _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.CANCELED;\n            case \"Postponed\":\n                return _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.POSTPONED;\n            case \"Rescheduled\":\n                return _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.RESCHEDULED;\n            default:\n                return _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventStatus.DRAFT;\n        }\n    };\n    return {\n        eventId: apiEvent.eventId,\n        name: apiEvent.title,\n        description: apiEvent.description || \"\",\n        category: _models_Event__WEBPACK_IMPORTED_MODULE_0__.EventCategory.CONCERT,\n        startDateTime,\n        endDateTime,\n        venueId: apiEvent.venueId,\n        venueName: \"\",\n        venueAddress: \"\",\n        poster: \"\",\n        organizerUserId: apiEvent.organizerId,\n        images: [],\n        details: apiEvent.description || \"\",\n        sectionPricing: apiEvent.sectionPricing.map((sp)=>({\n                eventId: apiEvent.eventId,\n                sectionId: sp.sectionId,\n                price: sp.price\n            })),\n        status: mapStatus(apiEvent.status),\n        createdAt: new Date(apiEvent.createdAt),\n        updatedAt: new Date(apiEvent.updatedAt)\n    };\n}\n/**\n * Convert Event model to CreateEventRequest for API\n */ function adaptEventToCreateRequest(event) {\n    // Handle both Event model and CreateEventDTO\n    let startDate;\n    let endDate;\n    if (event.startDateTime && event.endDateTime) {\n        // Event model format\n        startDate = event.startDateTime instanceof Date ? event.startDateTime : new Date(event.startDateTime);\n        endDate = event.endDateTime instanceof Date ? event.endDateTime : new Date(event.endDateTime);\n    } else if (event.startDateTime && event.endDateTime) {\n        // CreateEventDTO format (string dates)\n        startDate = new Date(event.startDateTime);\n        endDate = new Date(event.endDateTime);\n    } else {\n        throw new Error(\"Start and end date times are required\");\n    }\n    return {\n        title: event.name || event.title || \"\",\n        description: event.description,\n        venueId: event.venueId || \"\",\n        date: startDate.toISOString().split(\"T\")[0],\n        startTime: startDate.toTimeString().slice(0, 5),\n        endTime: endDate.toTimeString().slice(0, 5),\n        sectionPricing: event.sectionPricing || []\n    };\n}\n/**\n * Convert ApiVenue to Venue model\n */ function adaptApiVenueToVenue(apiVenue) {\n    var _apiVenue_sections;\n    return {\n        venueId: apiVenue.venueId,\n        name: apiVenue.name,\n        address: apiVenue.address,\n        city: apiVenue.city,\n        ownerUserId: apiVenue.ownerUserId || \"\",\n        sections: ((_apiVenue_sections = apiVenue.sections) === null || _apiVenue_sections === void 0 ? void 0 : _apiVenue_sections.map((section)=>{\n            var _section_seats;\n            return {\n                sectionId: section.sectionId,\n                venueId: section.venueId,\n                name: section.name,\n                capacity: section.capacity,\n                seats: ((_section_seats = section.seats) === null || _section_seats === void 0 ? void 0 : _section_seats.map((seat)=>({\n                        seatId: seat.seatId,\n                        sectionId: seat.sectionId,\n                        seatNumber: seat.seatNumber,\n                        rowNumber: seat.rowNumber,\n                        seatInRow: seat.seatInRow,\n                        status: \"available\"\n                    }))) || []\n            };\n        })) || [],\n        createdAt: apiVenue.createdAt,\n        updatedAt: apiVenue.updatedAt\n    };\n}\n/**\n * Store authentication token and user info\n */ function storeAuthData(authResponse) {\n    if (authResponse.token) {\n        // Store token in cookie (handled by BaseService interceptors)\n        document.cookie = \"token=\".concat(authResponse.token, \"; path=/; \").concat(authResponse.expiration ? \"expires=\".concat(new Date(authResponse.expiration).toUTCString(), \";\") : \"\");\n    }\n}\n/**\n * Clear authentication data\n */ function clearAuthData() {\n    document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n    document.cookie = \"refreshToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/apiAdapters.ts\n"));

/***/ })

});