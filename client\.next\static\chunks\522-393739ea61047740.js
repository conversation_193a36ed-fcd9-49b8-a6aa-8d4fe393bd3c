"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[522],{2522:(e,t,a)=>{a.d(t,{i$:()=>g,TL:()=>E,HB:()=>A,b9:()=>I,lh:()=>u,Ay:()=>L,SX:()=>h,fw:()=>i,vR:()=>l,ls:()=>y,nK:()=>p,P_:()=>C,qM:()=>v});var n=a(1990),r=a(3464),o=a(5809),s=a(9509);class d{parseEventDates(e){return{...e,createdAt:e.createdAt?new Date(e.createdAt):new Date,updatedAt:e.updatedAt?new Date(e.updatedAt):new Date,startDateTime:new Date(e.startDateTime),endDateTime:new Date(e.endDateTime)}}async getAllEvents(){try{return(await r.A.get("".concat(this.API_URL,"/events"))).data.map(e=>this.parseEventDates(e))}catch(t){console.warn("Failed to fetch events from API, using sample data");let{sampleEvents:e}=await a.e(972).then(a.bind(a,3972));return e}}async createEvent(e){try{let t=await r.A.post("".concat(this.API_URL,"/events"),e);return this.parseEventDates(t.data)}catch(e){o.zc.handleServiceErrorFromCatch(e,"Create event")}}async getEventById(e){try{let t=await r.A.get("".concat(this.API_URL,"/events/").concat(e));if(t.data)return this.parseEventDates(t.data);return null}catch(r){console.warn("Failed to fetch event ".concat(e," from API, trying sample data"));let{sampleEvents:t}=await a.e(972).then(a.bind(a,3972)),n=t.find(t=>t.eventId===e);if(n)return n;o.zc.handleServiceErrorFromCatch(r,"Fetch event by ID (".concat(e,")"))}}async rescheduleEvent(e,t){try{let a=await r.A.patch("".concat(this.API_URL,"/events/").concat(e,"/reschedule"),t);return this.parseEventDates(a.data)}catch(t){o.zc.handleServiceErrorFromCatch(t,"Reschedule event (".concat(e,")"))}}async deleteEvent(e){try{return(await r.A.delete("".concat(this.API_URL,"/events/").concat(e))).data}catch(t){o.zc.handleServiceErrorFromCatch(t,"Delete event (".concat(e,")"))}}async cancelEvent(e){try{let t=await r.A.patch("".concat(this.API_URL,"/events/").concat(e,"/cancel"));return this.parseEventDates(t.data)}catch(t){o.zc.handleServiceErrorFromCatch(t,"Cancel event (".concat(e,")"))}}async approveEvent(e){try{let t=await r.A.patch("".concat(this.API_URL,"/events/").concat(e,"/approve"));return this.parseEventDates(t.data)}catch(t){o.zc.handleServiceErrorFromCatch(t,"Approve event (".concat(e,")"))}}async postponeEvent(e){try{let t=await r.A.patch("".concat(this.API_URL,"/events/").concat(e,"/postpone"));return this.parseEventDates(t.data)}catch(t){o.zc.handleServiceErrorFromCatch(t,"Postpone event (".concat(e,")"))}}async submitEvent(e){try{let t=await r.A.patch("".concat(this.API_URL,"/events/").concat(e,"/submit-for-approval"));return this.parseEventDates(t.data)}catch(t){o.zc.handleServiceErrorFromCatch(t,"Submit event (".concat(e,")"))}}constructor(){this.API_URL=s.env.EVENT_API_URL||"http://localhost:8081",this.updateEvent=async(e,t)=>{try{let a=await r.A.patch("".concat(this.API_URL,"/events/").concat(e),t);return this.parseEventDates(a.data)}catch(t){o.zc.handleServiceErrorFromCatch(t,"Update event (".concat(e,")"))}}}}let c=new d,i=(0,n.zD)("events/fetchAll",async(e,t)=>{let{rejectWithValue:a}=t;try{return await c.getAllEvents()}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),l=(0,n.zD)("events/fetchById",async(e,t)=>{let{rejectWithValue:a}=t;try{return await c.getEventById(e)}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),u=(0,n.zD)("events/addNew",async(e,t)=>{let{rejectWithValue:a}=t;try{return await c.createEvent(e)}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),v=(0,n.zD)("events/updateEvent",async(e,t)=>{let{eventId:a,eventData:n}=e,{rejectWithValue:r}=t;try{return await c.updateEvent(a,n)}catch(e){return r(o.zc.handleAsyncThunkErrorFromCatch(e))}}),h=(0,n.zD)("events/remove",async(e,t)=>{let{rejectWithValue:a}=t;try{return await c.deleteEvent(e),e}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),p=(0,n.zD)("events/reschedule",async(e,t)=>{let{eventId:a,rescheduleData:n}=e,{rejectWithValue:r}=t;try{return await c.rescheduleEvent(a,n)}catch(e){return r(o.zc.handleAsyncThunkErrorFromCatch(e))}}),y=(0,n.zD)("events/postpone",async(e,t)=>{let{rejectWithValue:a}=t;try{return await c.postponeEvent(e)}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),E=(0,n.zD)("events/cancel",async(e,t)=>{let{rejectWithValue:a}=t;try{return await c.cancelEvent(e)}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),g=(0,n.zD)("events/approve",async(e,t)=>{let{rejectWithValue:a}=t;try{return await c.approveEvent(e)}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),C=(0,n.zD)("events/submit",async(e,t)=>{let{rejectWithValue:a}=t;try{return await c.submitEvent(e)}catch(e){return a(o.zc.handleAsyncThunkErrorFromCatch(e))}}),m=(0,n.Z0)({name:"events",initialState:{events:[],currentEvent:null,isLoadingList:!1,isLoadingDetails:!1,isLoadingMutation:!1,errorList:null,errorDetails:null,errorMutation:null},reducers:{clearCurrentEvent:e=>{e.currentEvent=null,e.errorDetails=null},clearMutationError:e=>{e.errorMutation=null}},extraReducers:e=>{e.addCase(i.pending,e=>{e.isLoadingList=!0,e.errorList=null}).addCase(i.fulfilled,(e,t)=>{e.isLoadingList=!1,e.events=t.payload}).addCase(i.rejected,(e,t)=>{e.isLoadingList=!1,e.errorList=t.payload}).addCase(l.pending,e=>{e.isLoadingDetails=!0,e.errorDetails=null,e.currentEvent=null}).addCase(l.fulfilled,(e,t)=>{e.isLoadingDetails=!1,e.currentEvent=t.payload}).addCase(l.rejected,(e,t)=>{e.isLoadingDetails=!1,e.errorDetails=t.payload}).addCase(u.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(u.fulfilled,(e,t)=>{e.isLoadingMutation=!1,e.events.push(t.payload)}).addCase(u.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(v.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(v.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let n=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==n&&(e.events[n]=t.payload),(null==(a=e.currentEvent)?void 0:a.eventId)===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(v.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(h.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(h.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1,e.events=e.events.filter(e=>e.eventId!==t.payload),(null==(a=e.currentEvent)?void 0:a.eventId)===t.payload&&(e.currentEvent=null)}).addCase(h.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(p.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(p.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let n=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==n&&(e.events[n]=t.payload),(null==(a=e.currentEvent)?void 0:a.eventId)===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(p.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(y.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(y.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let n=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==n&&(e.events[n]=t.payload),(null==(a=e.currentEvent)?void 0:a.eventId)===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(y.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(E.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(E.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let n=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==n&&(e.events[n]=t.payload),(null==(a=e.currentEvent)?void 0:a.eventId)===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(E.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(g.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(g.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let n=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==n&&(e.events[n]=t.payload),(null==(a=e.currentEvent)?void 0:a.eventId)===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(g.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload}).addCase(C.pending,e=>{e.isLoadingMutation=!0,e.errorMutation=null}).addCase(C.fulfilled,(e,t)=>{var a;e.isLoadingMutation=!1;let n=e.events.findIndex(e=>e.eventId===t.payload.eventId);-1!==n&&(e.events[n]=t.payload),(null==(a=e.currentEvent)?void 0:a.eventId)===t.payload.eventId&&(e.currentEvent=t.payload)}).addCase(C.rejected,(e,t)=>{e.isLoadingMutation=!1,e.errorMutation=t.payload})}}),{clearCurrentEvent:A,clearMutationError:I}=m.actions,L=m.reducer},5809:(e,t,a)=>{a.d(t,{zc:()=>o});var n=a(9362);class r{static normalize(e){var t,a,n;return"type"in e&&e.type?e:e instanceof Error?{type:"UNKNOWN",message:e.message}:"object"==typeof(t=e)&&null!==t&&"statusCode"in t?{type:"API",message:e.message,code:e.code,statusCode:e.statusCode}:"object"==typeof(a=e)&&null!==a&&"isNetworkError"in a?{type:"NETWORK",message:e.message}:"object"==typeof(n=e)&&null!==n&&"field"in n?{type:"VALIDATION",message:e.message}:{type:"UNKNOWN",message:e.message||"An unknown error occurred"}}static getMessage(e){return"string"==typeof e?e:"message"in e?e.message:"An unknown error occurred"}}class o{static handleAxiosError(e){if(!e.response)return{message:e.message||"Network error occurred",isNetworkError:!0,originalError:e};let t=e.response,a=t.data,n=(null==a?void 0:a.message)||(null==a?void 0:a.error)||e.message||"API error occurred",r=t.status;return{message:n,statusCode:r,details:null==a?void 0:a.details}}static handleStandardError(e){return r.normalize(e)}static handle(e){return e instanceof n.pe?this.handleAxiosError(e):this.handleStandardError(e)}static log(e,t){let a=t?"[".concat(t,"]"):"";"statusCode"in e&&void 0!==e.statusCode?e.statusCode>=500?console.error("".concat(a," Server Error:"),e.message,e):e.statusCode>=400?console.warn("".concat(a," Client Error:"),e.message,e):console.warn("".concat(a," API Error:"),e.message,e):"isNetworkError"in e?console.error("".concat(a," Network Error:"),e.message,e):console.error("".concat(a," Error:"),e.message,e)}static handleServiceError(e,t){let a=this.handle(e);throw this.log(a,t),a}static handleAsyncThunkError(e){return this.handle(e).message}static handleUnknown(e){return e&&"object"==typeof e&&"isAxiosError"in e||e instanceof Error?e:Error("string"==typeof e?e:"An unknown error occurred")}static fromCatch(e){let t=this.handleUnknown(e);return this.handle(t)}static handleServiceErrorFromCatch(e,t){let a=this.handleUnknown(e);return this.handleServiceError(a,t)}static handleAsyncThunkErrorFromCatch(e){let t=this.handleUnknown(e);return this.handleAsyncThunkError(t)}}}}]);