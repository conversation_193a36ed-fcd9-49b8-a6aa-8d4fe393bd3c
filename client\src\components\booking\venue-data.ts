import { Venue, Seat } from "@/models/Venue";

const generateSeats = (sectionId: string, numRows: number, seatsPerRow: number): Seat[] => {
  const seats: Seat[] = [];
  const rowNames = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  for (let i = 0; i < numRows; i++) {
    const rowName = rowNames[i];
    for (let j = 1; j <= seatsPerRow; j++) {
      const randomStatus =
        Math.random() < 0.1 ? "sold" : Math.random() < 0.1 ? "pending" : "available";

      seats.push({
        seatId: `${sectionId}-${rowName}-${j}`,
        sectionId,
        seatNumber: j.toString(),
        rowNumber: rowName,
        seatInRow: j,
        status: randomStatus,
      });
    }
  }

  return seats;
};

export const sampleVenue: Venue = {
  venueId: "venue-1",
  name: "America First Field",
  address: "123 Main St",
  city: "Sandy, Utah, USA",
  ownerUserId: "user-1",
  createdAt: "2025-04-01T10:00:00Z",
  updatedAt: "2025-04-10T12:00:00Z",
  sections: [
    // top sections
    {
      sectionId: "section-1",
      venueId: "venue-1",
      name: "1",
      price: 50,
      x: 70,
      y: 0,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-1", 14, 8),
    },
    {
      sectionId: "section-2",
      name: "2",
      venueId: "venue-1",
      price: 50,
      x: 160,
      y: 0,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-2", 14, 8),
    },
    {
      sectionId: "section-3",
      name: "3",
      venueId: "venue-1",
      price: 50,
      x: 250,
      y: 0,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-3", 14, 8),
    },
    {
      sectionId: "section-4",
      name: "4",
      venueId: "venue-1",
      price: 60,
      x: 340,
      y: 0,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-4", 14, 8),
    },
    {
      sectionId: "section-5",
      name: "5",
      venueId: "venue-1",
      price: 70,
      x: 430,
      y: 0,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-5", 14, 8),
    },
    // right sections
    {
      sectionId: "section-6",
      name: "6",
      venueId: "venue-1",
      price: 75,
      x: 600,
      y: 180,
      width: 80,
      height: 160,
      rotation: 90,
      capacity: 14 * 8,
      seats: generateSeats("section-6", 14, 8),
    },
    {
      sectionId: "section-7",
      name: "7",
      venueId: "venue-1",
      price: 80,
      x: 600,
      y: 280,
      width: 80,
      height: 160,
      rotation: 90,
      capacity: 14 * 8,
      seats: generateSeats("section-7", 14, 8),
    },
    {
      sectionId: "section-8",
      name: "8",
      venueId: "venue-1",
      price: 80,
      x: 600,
      y: 380,
      width: 80,
      height: 160,
      rotation: 90,
      capacity: 14 * 8,
      seats: generateSeats("section-8", 14, 8),
    },
    // bottom sections
    {
      sectionId: "section-9",
      name: "9",
      venueId: "venue-1",
      price: 50,
      x: 70,
      y: 570,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-9", 14, 8),
    },
    {
      sectionId: "section-10",
      name: "10",
      venueId: "venue-1",
      price: 50,
      x: 160,
      y: 570,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-10", 14, 8),
    },
    {
      sectionId: "section-11",
      name: "11",
      venueId: "venue-1",
      price: 50,
      x: 250,
      y: 570,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-11", 14, 8),
    },
    {
      sectionId: "section-12",
      name: "12",
      venueId: "venue-1",
      price: 60,
      x: 340,
      y: 570,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-12", 14, 8),
    },
    {
      sectionId: "section-13",
      name: "13",
      venueId: "venue-1",
      price: 70,
      x: 430,
      y: 570,
      width: 80,
      height: 160,
      capacity: 14 * 8,
      seats: generateSeats("section-13", 14, 8),
    },
    // left sections
    {
      sectionId: "section-14",
      name: "14",
      venueId: "venue-1",
      price: 75,
      x: -100,
      y: 180,
      width: 80,
      height: 160,
      rotation: 90,
      capacity: 14 * 8,
      seats: generateSeats("section-14", 14, 8),
    },
    {
      sectionId: "section-15",
      name: "15",
      venueId: "venue-1",
      price: 80,
      x: -100,
      y: 280,
      width: 80,
      height: 160,
      rotation: 90,
      capacity: 14 * 8,
      seats: generateSeats("section-15", 14, 8),
    },
    {
      sectionId: "section-16",
      name: "16",
      venueId: "venue-1",
      price: 80,
      x: -100,
      y: 380,
      width: 80,
      height: 160,
      rotation: 90,
      capacity: 14 * 8,
      seats: generateSeats("section-16", 14, 8),
    },
    // corner sections
    {
      sectionId: "section-17",
      name: "17",
      venueId: "venue-1",
      price: 80,
      x: -80,
      y: 30,
      width: 120,
      height: 160,
      rotation: 315,
      capacity: 14 * 8,
      seats: generateSeats("section-17", 14, 8),
    },
    {
      sectionId: "section-18",
      name: "18",
      venueId: "venue-1",
      price: 75,
      x: 580,
      y: 30,
      width: 120,
      height: 160,
      rotation: 45,
      capacity: 14 * 8,
      seats: generateSeats("section-18", 14, 8),
    },
    {
      sectionId: "section-19",
      name: "19",
      venueId: "venue-1",
      price: 75,
      x: 580,
      y: 530,
      width: 120,
      height: 160,
      rotation: 135,
      capacity: 14 * 8,
      seats: generateSeats("section-19", 14, 8),
    },
    {
      sectionId: "section-20",
      name: "20",
      venueId: "venue-1",
      price: 80,
      x: -80,
      y: 530,
      width: 120,
      height: 160,
      rotation: 225,
      capacity: 14 * 8,
      seats: generateSeats("section-20", 14, 8),
    },
  ],
};
