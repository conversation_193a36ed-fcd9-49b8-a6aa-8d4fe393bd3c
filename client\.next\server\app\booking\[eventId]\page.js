(()=>{var e={};e.id=208,e.ids=[208],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,t,s)=>{"use strict";var i=s(65773);s.o(i,"useParams")&&s.d(t,{useParams:function(){return i.useParams}}),s.o(i,"useRouter")&&s.d(t,{useRouter:function(){return i.useRouter}})},17233:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>c});var i=s(65239),r=s(48088),n=s(88170),a=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["booking",{children:["[eventId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,64410)),"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\[eventId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,53039)),"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\[eventId]\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/booking/[eventId]/page",pathname:"/booking/[eventId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var i=s(60687);s(43210);var r=s(81391),n=s(24224),a=s(76279);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-darkText shadow-xs hover:bg-primary/70",outline:"border bg-transparent border-darkStroke shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:n=!1,...l}){let c=n?r.DX:"button";return(0,i.jsx)(c,{"data-slot":"button",className:(0,a.cn)(o({variant:t,size:s,className:e})),...l})}},30474:(e,t,s)=>{"use strict";s.d(t,{default:()=>r.a});var i=s(31261),r=s.n(i)},31261:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return l},getImageProps:function(){return o}});let i=s(14985),r=s(44953),n=s(46533),a=i._(s(1933));function o(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let l=n.Image},33873:e=>{"use strict";e.exports=require("path")},35920:(e,t,s)=>{"use strict";s.d(t,{mJ:()=>p,KC:()=>m,Qb:()=>f});var i=s(43210),r=s(3377);let n=e=>e.events.events,a=e=>e.events.currentEvent,o=e=>e.events.isLoadingList,l=e=>e.events.isLoadingDetails,c=e=>e.events.isLoadingMutation,d=e=>e.events.errorList,h=e=>e.events.errorDetails,u=e=>e.events.errorMutation;var x=s(85908);let m=()=>{let e=(0,x.j)(),t=(0,x.G)(n),s=(0,x.G)(o);return{events:t,isLoadingList:s,errorEventList:(0,x.G)(d),loadEvents:(0,i.useCallback)(()=>e((0,r.fw)()),[e])}},p=()=>{let e=(0,x.j)(),t=(0,x.G)(a),s=(0,x.G)(l),n=(0,x.G)(h);return{event:t,isLoadingEventDetails:s,errorEventDetails:n,loadEvent:(0,i.useCallback)(t=>e((0,r.vR)(t)),[e]),clearDetails:(0,i.useCallback)(()=>{e((0,r.HB)())},[e])}},f=()=>{let e=(0,x.j)(),t=(0,x.G)(c),s=(0,x.G)(u),n=(0,i.useCallback)(t=>e((0,r.lh)(t)).unwrap(),[e]),a=(0,i.useCallback)((t,s)=>e((0,r.qM)({eventId:t,eventData:s})).unwrap(),[e]),o=(0,i.useCallback)(t=>e((0,r.SX)(t)).unwrap(),[e]),l=(0,i.useCallback)((t,s)=>e((0,r.nK)({eventId:t,rescheduleData:s})).unwrap(),[e]),d=(0,i.useCallback)(t=>e((0,r.ls)(t)).unwrap(),[e]),h=(0,i.useCallback)(t=>e((0,r.TL)(t)).unwrap(),[e]),m=(0,i.useCallback)(t=>e((0,r.i$)(t)).unwrap(),[e]);return{isLoadingEventMuatation:t,errorEventMutation:s,createEvent:n,updateEvent:a,removeEvent:o,approveEvent:m,rescheduleEvent:l,postponeEvent:d,cancelEvent:h,submitEvent:(0,i.useCallback)(t=>e((0,r.P_)(t)).unwrap(),[e]),clearError:(0,i.useCallback)(()=>{e((0,r.b9)())},[e])}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55990:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var i=s(60687),r=s(43210);let n=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var a=s(30474);function o({eventName:e,stadium:t,location:s,date:r,time:o,section:l,row:c,seats:d,ticketPrice:h,quantity:u,onConfirm:x,onClose:m}){return(0,i.jsx)("div",{className:"fixed inset-0 flex items-center justify-center bg-black/50 z-50",children:(0,i.jsxs)("div",{className:" bg-white rounded-lg shadow-lg w-[450px]",children:[(0,i.jsxs)("div",{className:"relative w-full h-[200px] bg-gray-200 rounded-t-lg overflow-hidden",children:[(0,i.jsx)(a.default,{src:"https://images.unsplash.com/photo-1558465202-92356bf74344?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",alt:"Event",width:450,height:200,className:"w-full h-full object-cover object-center"}),(0,i.jsx)(n,{color:"#000",className:"absolute top-4 right-4 cursor-pointer hover:bg-gray-100 rounded-full",onClick:m})]}),(0,i.jsxs)("div",{className:"p-4",children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-center text-[#1D1D1D] mb-2",children:e}),(0,i.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-4",children:[(0,i.jsx)("p",{children:t}),(0,i.jsx)("p",{children:s})]}),(0,i.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-4",children:[(0,i.jsx)("p",{children:r}),(0,i.jsx)("p",{children:o})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["Section ",l]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["Row ",c]})]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["Seats ",d]})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["Ticket price: ",(0,i.jsxs)("span",{className:"text-[#02471F] font-bold",children:["$",h," each"]})]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["Quantity: ",(0,i.jsxs)("span",{className:"font-bold",children:[u," tickets"]})]})]}),(0,i.jsx)("button",{onClick:x,className:"w-full bg-[#2ECC71] text-white font-bold py-2 rounded-lg hover:bg-[#28a65c] transition",children:"Confirm"})]})]})})}let l=(e,t,s)=>{let i=[];for(let r=0;r<t;r++){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZ"[r];for(let r=1;r<=s;r++){let s=.1>Math.random()?"sold":.1>Math.random()?"pending":"available";i.push({seatId:`${e}-${t}-${r}`,sectionId:e,seatNumber:r.toString(),rowNumber:t,seatInRow:r,status:s})}}return i},c={venueId:"venue-1",name:"America First Field",address:"123 Main St",city:"Sandy, Utah, USA",ownerUserId:"user-1",createdAt:"2025-04-01T10:00:00Z",updatedAt:"2025-04-10T12:00:00Z",sections:[{sectionId:"section-1",venueId:"venue-1",name:"1",price:50,x:70,y:0,width:80,height:160,capacity:112,seats:l("section-1",14,8)},{sectionId:"section-2",name:"2",venueId:"venue-1",price:50,x:160,y:0,width:80,height:160,capacity:112,seats:l("section-2",14,8)},{sectionId:"section-3",name:"3",venueId:"venue-1",price:50,x:250,y:0,width:80,height:160,capacity:112,seats:l("section-3",14,8)},{sectionId:"section-4",name:"4",venueId:"venue-1",price:60,x:340,y:0,width:80,height:160,capacity:112,seats:l("section-4",14,8)},{sectionId:"section-5",name:"5",venueId:"venue-1",price:70,x:430,y:0,width:80,height:160,capacity:112,seats:l("section-5",14,8)},{sectionId:"section-6",name:"6",venueId:"venue-1",price:75,x:600,y:180,width:80,height:160,rotation:90,capacity:112,seats:l("section-6",14,8)},{sectionId:"section-7",name:"7",venueId:"venue-1",price:80,x:600,y:280,width:80,height:160,rotation:90,capacity:112,seats:l("section-7",14,8)},{sectionId:"section-8",name:"8",venueId:"venue-1",price:80,x:600,y:380,width:80,height:160,rotation:90,capacity:112,seats:l("section-8",14,8)},{sectionId:"section-9",name:"9",venueId:"venue-1",price:50,x:70,y:570,width:80,height:160,capacity:112,seats:l("section-9",14,8)},{sectionId:"section-10",name:"10",venueId:"venue-1",price:50,x:160,y:570,width:80,height:160,capacity:112,seats:l("section-10",14,8)},{sectionId:"section-11",name:"11",venueId:"venue-1",price:50,x:250,y:570,width:80,height:160,capacity:112,seats:l("section-11",14,8)},{sectionId:"section-12",name:"12",venueId:"venue-1",price:60,x:340,y:570,width:80,height:160,capacity:112,seats:l("section-12",14,8)},{sectionId:"section-13",name:"13",venueId:"venue-1",price:70,x:430,y:570,width:80,height:160,capacity:112,seats:l("section-13",14,8)},{sectionId:"section-14",name:"14",venueId:"venue-1",price:75,x:-100,y:180,width:80,height:160,rotation:90,capacity:112,seats:l("section-14",14,8)},{sectionId:"section-15",name:"15",venueId:"venue-1",price:80,x:-100,y:280,width:80,height:160,rotation:90,capacity:112,seats:l("section-15",14,8)},{sectionId:"section-16",name:"16",venueId:"venue-1",price:80,x:-100,y:380,width:80,height:160,rotation:90,capacity:112,seats:l("section-16",14,8)},{sectionId:"section-17",name:"17",venueId:"venue-1",price:80,x:-80,y:30,width:120,height:160,rotation:315,capacity:112,seats:l("section-17",14,8)},{sectionId:"section-18",name:"18",venueId:"venue-1",price:75,x:580,y:30,width:120,height:160,rotation:45,capacity:112,seats:l("section-18",14,8)},{sectionId:"section-19",name:"19",venueId:"venue-1",price:75,x:580,y:530,width:120,height:160,rotation:135,capacity:112,seats:l("section-19",14,8)},{sectionId:"section-20",name:"20",venueId:"venue-1",price:80,x:-80,y:530,width:120,height:160,rotation:225,capacity:112,seats:l("section-20",14,8)}]};var d=s(29523);let h=function({section:e,detailLevel:t,isSelected:s,selectedSeats:n,onSectionClick:a,onSeatSelect:o,scale:l}){let[c,d]=(0,r.useState)("#6dfe4a"),[h,u]=(0,r.useState)({}),x=s?1:.9,m=(s?2:1)/l;if("section"===t)return(0,i.jsxs)("g",{transform:`translate(${e.x}, ${e.y}) ${e.rotation?`rotate(${e.rotation})`:""}`,onClick:a,style:{cursor:"pointer"},children:[(0,i.jsx)("rect",{x:-(e.width||40)/2,y:-(e.height||40)/2,width:e.width,height:e.height,fill:c,opacity:x,stroke:"#000",strokeWidth:m,rx:2,ry:2}),(0,i.jsx)("g",{transform:`${e.rotation?`rotate(${-e.rotation})`:""}`,children:(0,i.jsx)("text",{x:0,y:0,textAnchor:"middle",dominantBaseline:"middle",fontSize:12/l,fontWeight:"bold",fill:"#fff",children:e.name})}),(0,i.jsxs)("g",{transform:`translate(0, ${(e.height??40)/2+10}) ${e.rotation?`rotate(${-e.rotation})`:""}`,children:[(0,i.jsx)("rect",{x:-20/l,y:-10/l,width:40/l,height:20/l,rx:5/l,fill:"black",stroke:"#000",strokeWidth:.5/l}),(0,i.jsxs)("text",{textAnchor:"middle",dominantBaseline:"middle",fontSize:10/l,fontWeight:"bold",fill:"white",children:["$",e.price]})]})]});if("row"===t){let t=Object.keys(e.seats.reduce((e,t)=>(e[t.rowNumber]||(e[t.rowNumber]=[]),e[t.rowNumber].push(t),e),{})).sort(),s=(e.height||100)/t.length;return(0,i.jsxs)("g",{transform:`translate(${e.x}, ${e.y}) ${e.rotation?`rotate(${e.rotation})`:""}`,children:[(0,i.jsx)("rect",{x:-(e.width||100)/2,y:-(e.height||100)/2,width:e.width||100,height:e.height||100,fill:c,opacity:.3,stroke:"#000",strokeWidth:m,rx:2,ry:2}),t.map((t,r)=>{let n=-(e.height||100)/2+r*s,o=h[t]||c;return(0,i.jsxs)("g",{onClick:a,style:{cursor:"pointer"},children:[(0,i.jsx)("rect",{x:-(e.width||100)/2,y:n,width:e.width||100,height:s,fill:o,opacity:x,stroke:"#000",strokeWidth:.5/l}),(0,i.jsxs)("text",{x:0,y:n+s/2,textAnchor:"middle",dominantBaseline:"middle",fontSize:12/l,fill:"#fff",children:["Row ",t]})]},t)}),(0,i.jsx)("g",{transform:`${e.rotation?`rotate(${-e.rotation})`:""}`,children:(0,i.jsx)("text",{x:0,y:-(e.height||100)/2-10,textAnchor:"middle",dominantBaseline:"middle",fontSize:14/l,fontWeight:"bold",fill:"#000",children:e.name})}),(0,i.jsxs)("g",{transform:`translate(0, ${(e.height||100)/2+15}) ${e.rotation?`rotate(${-e.rotation})`:""}`,children:[(0,i.jsx)("rect",{x:-20/l,y:-10/l,width:40/l,height:20/l,rx:5/l,fill:"black",stroke:"#000",strokeWidth:.5/l}),(0,i.jsxs)("text",{textAnchor:"middle",dominantBaseline:"middle",fontSize:10/l,fontWeight:"bold",fill:"white",children:["$",e.price]})]})]})}if("seat"===t){let t=e.seats.reduce((e,t)=>(e[t.rowNumber]||(e[t.rowNumber]=[]),e[t.rowNumber].push(t),e),{}),s=Object.keys(t).sort(),r=(e.height||100)/s.length;return(0,i.jsxs)("g",{transform:`translate(${e.x}, ${e.y}) ${e.rotation?`rotate(${e.rotation})`:""}`,children:[(0,i.jsx)("rect",{x:-(e.width||100)/2,y:-(e.height||100)/2,width:e.width||100,height:e.height||100,fill:c,opacity:.3,stroke:"#000",strokeWidth:m,rx:2,ry:2}),(0,i.jsx)("g",{transform:`${e.rotation?`rotate(${-e.rotation})`:""}`,children:(0,i.jsx)("text",{x:0,y:-(e.height||100)/2-10,textAnchor:"middle",dominantBaseline:"middle",fontSize:16/l,fontWeight:"bold",fill:"#000",children:e.name})}),(0,i.jsxs)("g",{transform:`translate(0, ${(e.height||100)/2+15}) ${e.rotation?`rotate(${-e.rotation})`:""}`,children:[(0,i.jsx)("rect",{x:-20/l,y:-10/l,width:40/l,height:20/l,rx:5/l,fill:"black",stroke:"#000",strokeWidth:.5/l}),(0,i.jsxs)("text",{textAnchor:"middle",dominantBaseline:"middle",fontSize:10/l,fontWeight:"bold",fill:"white",children:["$",e.price]})]}),s.map((s,a)=>{let c=-(e.height||100)/2+a*r,d=t[s].sort((e,t)=>e.seatInRow-t.seatInRow),h=(e.width||100)/d.length,u=Math.min(.8*h,.8*r)/2;return(0,i.jsxs)("g",{children:[(0,i.jsx)("text",{x:-(e.width||100)/2-10,y:c+r/2,textAnchor:"middle",dominantBaseline:"middle",fontSize:12/l,fontWeight:"bold",fill:"#000",children:s}),d.map((t,s)=>{let a="#ccc";"available"===t.status?a="#4CAF50":"sold"===t.status?a="#F44336":"pending"===t.status&&(a="#FFC107");let d=n.includes(t.seatId);d&&(a="#2196F3");let x=-(e.width||100)/2+s*h+h/2;return(0,i.jsxs)("g",{transform:`translate(${x}, ${c+r/2})`,onClick:()=>{("available"===t.status||d)&&o(t.seatId)},style:{cursor:"available"===t.status||d?"pointer":"not-allowed"},children:[(0,i.jsx)("circle",{r:u,fill:a,stroke:"#000",strokeWidth:.5/l,opacity:"sold"===t.status?.7:1}),(0,i.jsx)("text",{textAnchor:"middle",dominantBaseline:"middle",fontSize:10/l,fontWeight:"bold",fill:d||"available"===t.status?"#fff":"#000",children:t.seatNumber})]},t.seatId)})]},s)})]})}return null},u=function({venue:e,selectedSeats:t,onSeatSelect:s}){let n=(0,r.useRef)(null),a=(0,r.useRef)(null),[o,l]=(0,r.useState)({width:800,height:600}),[c,u]=(0,r.useState)(1),[x,m]=(0,r.useState)({x:400,y:300}),[p,f]=(0,r.useState)(!1),[v,g]=(0,r.useState)({x:0,y:0}),[w,b]=(0,r.useState)("section"),[y,j]=(0,r.useState)(null),k=e=>{j(e),"section"===w&&(u(2),m({x:o.width/2-(e.x??0)*c,y:o.height/2-(e.y??0)*c}))};return(0,i.jsxs)("div",{ref:n,className:"relative w-full h-full",children:[(0,i.jsxs)("div",{className:"absolute top-4 right-4 z-10 flex flex-col gap-2",children:[(0,i.jsx)(d.$,{onClick:()=>{u(Math.min(1.2*c,5))},className:"w-10 h-10 p-0 rounded-full bg-green-500 hover:bg-green-600 text-white font-bold",children:"+"}),(0,i.jsx)(d.$,{onClick:()=>{u(Math.max(.8*c,.5))},className:"w-10 h-10 p-0 rounded-full bg-green-500 hover:bg-green-600 text-white font-bold",children:"-"}),(0,i.jsx)(d.$,{onClick:()=>{u(1),m({x:o.width/2,y:o.height/2}),j(null),b("section")},className:"w-10 h-10 p-0 rounded-full bg-green-500 hover:bg-green-600 text-white font-bold",children:"↺"})]}),(0,i.jsx)("svg",{ref:a,width:o.width,height:o.height,viewBox:`0 0 ${o.width} ${o.height}`,onWheel:e=>{e.preventDefault();let t=Math.min(Math.max(e.deltaY>0?.9*c:1.1*c,.5),5),s=a.current?.getBoundingClientRect();if(!s)return;let i=e.clientX-s.left,r=e.clientY-s.top,n=i-(i-x.x)*(t/c),o=r-(r-x.y)*(t/c);u(t),m({x:n,y:o})},onMouseDown:e=>{f(!0),g({x:e.clientX,y:e.clientY})},onMouseMove:e=>{if(!p)return;let t=e.clientX-v.x,s=e.clientY-v.y;m({x:x.x+t,y:x.y+s}),g({x:e.clientX,y:e.clientY})},onMouseUp:()=>{f(!1)},onMouseLeave:()=>{f(!1)},className:"touch-none",style:{background:"#f8f9fa"},children:(0,i.jsxs)("g",{transform:`translate(${x.x}, ${x.y}) scale(${c})`,children:[(0,i.jsx)("rect",{x:0,y:100,width:500,height:367,fill:"#4CAF50"}),Array.from({length:10}).map((e,t)=>(0,i.jsx)("rect",{x:50*t,y:100,width:50,height:367,fill:t%2==0?"#43A047":"#4CAF50"},t)),(0,i.jsx)("rect",{x:50,y:150,width:400,height:267,fill:"none",stroke:"#fff",strokeWidth:2/c}),(0,i.jsxs)("g",{stroke:"#fff",strokeWidth:1/c,fill:"none",children:[(0,i.jsx)("line",{x1:250,y1:150,x2:250,y2:417}),(0,i.jsx)("circle",{cx:250,cy:283.5,r:45}),(0,i.jsx)("rect",{x:50,y:208,width:80,height:150}),(0,i.jsx)("rect",{x:370,y:208,width:80,height:150}),(0,i.jsx)("rect",{x:50,y:245,width:30,height:75}),(0,i.jsx)("rect",{x:420,y:245,width:30,height:75}),(0,i.jsx)("rect",{x:40,y:258,width:10,height:50}),(0,i.jsx)("rect",{x:450,y:258,width:10,height:50}),(0,i.jsx)("path",{d:"M 130 283.5 A 45 45 0 0 1 130 283.5"}),(0,i.jsx)("path",{d:"M 370 283.5 A 45 45 0 0 O 370 283.5"})]}),e.sections.map(e=>(0,i.jsx)(h,{section:e,detailLevel:w,isSelected:y?.sectionId===e.sectionId,selectedSeats:t,onSectionClick:()=>k(e),onSeatSelect:s,scale:c},e.sectionId))]})})]})},x=function({onSeatSelect:e,selectedSeats:t}){let[s,n]=(0,r.useState)([]),a=t?t.map(e=>e.SeatId):s;return(0,i.jsx)("div",{className:"w-full h-full bg-white rounded-md shadow-md overflow-hidden",children:(0,i.jsx)(u,{venue:c,selectedSeats:a,onSeatSelect:t=>{if(e)for(let s of c.sections){let i=s.seats.find(e=>e.seatId===t);if(i)return void e({SeatId:i.seatId,SectionId:s.sectionId,SeatNumber:i.seatNumber,RowNumber:i.rowNumber,SeatInRow:parseInt(i.seatNumber)})}else n(e=>e.includes(t)?e.filter(e=>e!==t):[...e,t])}})})};var m=s(84994);function p({seatOrder:e,onRemove:t}){return(0,i.jsxs)("div",{className:"w-[90%] relative h-[80px] border-1 border-[#000000] rounded-[4px] flex flex-row",children:[(0,i.jsx)("div",{className:"aspect-[1/2] w-[24%] overflow-hidden rounded-[4px]",children:(0,i.jsx)(a.default,{src:m.A,alt:"homeBackground",className:"w-full h-full object-cover object-center rounded-[4px]"})}),(0,i.jsxs)("div",{className:"w-[75%] px-2 flex flex-col items-start justify-between",children:[(0,i.jsxs)("div",{className:"w-full flex flex-col",children:[(0,i.jsx)("p",{className:"text-[#1D1D1D] text-[16px] font-bold",children:e.SectionId}),(0,i.jsxs)("p",{className:"text-[#686868] text-[14px] font-medium",children:[e.RowNumber," | ",e.SeatNumber]})]}),(0,i.jsxs)("div",{className:"w-full flex flex-row items-center justify-between",children:[(0,i.jsx)("p",{className:"text-[#686868] text-[14px] font-medium",children:"Viewed By 9 People"}),(0,i.jsx)("p",{className:"text-[#02471F] text-[16px] font-bold",children:"$10"})]})]}),(0,i.jsx)("div",{className:"absolute top-1 right-1 w-[15px] h-[15px] rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-300",onClick:()=>t(e.SeatId),children:"x"})]})}var f=s(35920),v=s(16189);function g(){let{eventId:e}=(0,v.useParams)(),t=(0,v.useRouter)(),[s,n]=(0,r.useState)([]),[a,l]=(0,r.useState)(!1),{event:c,isLoadingEventDetails:h,errorEventDetails:u,loadEvent:m,clearDetails:g}=(0,f.mJ)(),w=e=>{n(t=>t.filter(t=>t.SeatId!==e))};return h?(0,i.jsx)("div",{className:"w-full h-[calc(100vh-80px)] flex items-center justify-center",children:(0,i.jsx)("div",{className:"text-lg",children:"Loading event details..."})}):u?(0,i.jsx)("div",{className:"w-full h-[calc(100vh-80px)] flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-lg text-red-500",children:["Error: ",u]})}):c?(0,i.jsxs)("div",{className:"w-full h-[calc(100vh-80px)] py-3 px-5 flex flex-row",children:[(0,i.jsxs)("div",{className:"w-[40%] h-full py-3 px-4 overflow-y-auto",children:[(0,i.jsxs)("div",{className:"bookingInfo w-full flex flex-col gap-3 border-b-1 border-[#D0D0D0] pb-3 mb-3",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-[#1D1D1D]",children:c?.name}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("p",{className:"text-[#686868] text-[12px] font-medium",children:c?.startDateTime?new Date(c.startDateTime).toLocaleString():"Start time not available"}),(0,i.jsx)("p",{className:"text-[#686868] text-[12px] font-medium",children:c?.endDateTime?new Date(c.endDateTime).toLocaleString():"End time not available"})]})]}),(0,i.jsxs)("div",{className:"w-full flex flex-row justify-between mb-2",children:[(0,i.jsxs)("p",{className:"text-[#1D1D1D] text-[14px] font-semibold",children:[s.length," Selected Seats"]}),(0,i.jsxs)("p",{className:"text-[#02471F] text-[16px] font-bold",children:["Total price: $",10*s.length]})]}),(0,i.jsx)("div",{className:"selectedSeats flex flex-col items-center gap-3",children:s.map((e,t)=>(0,i.jsx)(p,{seatOrder:e,onRemove:w},t))})]}),(0,i.jsx)("div",{className:"w-[60%] h-full",children:(0,i.jsx)(x,{selectedSeats:s,onSeatSelect:e=>{n(t=>t.find(t=>t.SeatId===e.SeatId)?t.filter(t=>t.SeatId!==e.SeatId):[...t,e])}})}),a&&(0,i.jsx)(o,{eventName:"FC Barcelona vs Real Madrid",stadium:"My Dinh Stadium",location:"Ha Noi, Vietnam",date:"Mar 22 • Sat • 2025",time:"19:30 - 23:30",section:"99",row:"C",seats:"4-10",ticketPrice:10,quantity:7,onConfirm:()=>{},onClose:()=>l(!1)}),(0,i.jsx)(d.$,{className:"fixed bottom-4 left-1/6 py-3 px-8 text-xl font-bold rounded-4xl",onClick:()=>{t.push(`/booking/payment/${e}`)},children:"Book"})]}):(0,i.jsx)("div",{className:"w-full h-[calc(100vh-80px)] flex items-center justify-center",children:(0,i.jsx)("div",{className:"text-lg",children:"Event not found"})})}},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var i=s(43210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),a=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim();var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:n="",children:a,iconNode:c,...d},h)=>(0,i.createElement)("svg",{ref:h,...l,width:t,height:t,stroke:e,strokeWidth:r?24*Number(s)/Number(t):s,className:o("lucide",n),...d},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(a)?a:[a]])),d=(e,t)=>{let s=(0,i.forwardRef)(({className:s,...n},l)=>(0,i.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${r(a(e))}`,`lucide-${e}`,s),...n}));return s.displayName=a(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64410:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Study\\\\Classes\\\\6thSemester\\\\SoftwareArchitecture\\\\project\\\\client\\\\src\\\\app\\\\booking\\\\[eventId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\[eventId]\\page.tsx","default")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var i=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},76279:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var i=s(49384),r=s(82348);function n(...e){return(0,r.QP)((0,i.$)(e))}},79551:e=>{"use strict";e.exports=require("url")},79733:(e,t,s)=>{Promise.resolve().then(s.bind(s,64410))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84994:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i={src:"/_next/static/media/Home_Background.20663903.png",height:720,width:1440,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAAKlBMVEUZJSk4SEuZ0V1FTFUtPEImMjaNzE+RrXRgij2GomqZtIF8hnk9U1hvgWZX8Dr5AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJ0lEQVR4nAXBhwEAIAwCMKC76v/vmgBB8jqAoJk5cKZ2ax46JSn7AwlSAJxkVFnPAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:4}},85908:(e,t,s)=>{"use strict";s.d(t,{G:()=>n,j:()=>r});var i=s(54864);let r=i.wA.withTypes(),n=i.d4.withTypes()},93301:(e,t,s)=>{Promise.resolve().then(s.bind(s,55990))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[447,423,658,23,695],()=>s(17233));module.exports=i})();