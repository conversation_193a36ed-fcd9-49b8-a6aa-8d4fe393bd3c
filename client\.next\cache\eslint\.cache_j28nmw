[{"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\layout.tsx": "1", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\page.tsx": "2", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\index.ts": "3", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\button.tsx": "4", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\constants\\index.ts": "5", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\utils.ts": "6", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\index.ts": "7", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\index.ts": "8", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\admin\\payments\\page.tsx": "9", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\admin\\payments\\[id]\\page.tsx": "10", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\[eventId]\\page.tsx": "11", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\event-detail\\[id]\\page.tsx": "12", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\orders\\page.tsx": "13", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\event\\page.tsx": "14", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\event\\[id]\\page.tsx": "15", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\page.tsx": "16", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\ConfirmPopup.tsx": "17", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\SeatOrderCard.tsx": "18", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\event-detail\\InfoNavigator.tsx": "19", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\Header.tsx": "20", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\home\\EventInfo.tsx": "21", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\orders\\OrderCard.tsx": "22", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\dropdown.tsx": "23", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\fonts.ts": "24", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\place-holder.data.ts": "25", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Event.ts": "26", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Seat.ts": "27", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\auth\\[slug]\\page.tsx": "28", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\payment\\[eventId]\\page.tsx": "29", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\orders\\[id]\\page.tsx": "30", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\provider.tsx": "31", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\ForgotPasswordForm.tsx": "32", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\LoginForm.tsx": "33", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\SignUpForm.tsx": "34", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\SeatMap.tsx": "35", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TicketCard.tsx": "36", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TimeCount.tsx": "37", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TimeInfoConfirmPopup.tsx": "38", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\venue-data.ts": "39", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\VenueMapSVG.tsx": "40", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\VenueSectionSVG.tsx": "41", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\loading.tsx": "42", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\textinput.tsx": "43", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\redux.ts": "44", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useBooking.ts": "45", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useEvents.ts": "46", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useUser.ts": "47", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useVenue.ts": "48", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\EventDTO.ts": "49", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\UserDTO.ts": "50", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\VenueDTO.ts": "51", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\User.ts": "52", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Venue.ts": "53", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\eventService.ts": "54", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\userService.ts": "55", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\venueService.ts": "56", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\booking\\bookingSelector.ts": "57", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\booking\\bookingSlice.ts": "58", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\event\\eventSelector.ts": "59", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\event\\eventSlice.ts": "60", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\store.ts": "61", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\user\\userSelector.ts": "62", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\user\\userSlice.ts": "63", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\venue\\venueSelector.ts": "64", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\venue\\venueSlice.ts": "65", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\utils\\seatConverter.ts": "66", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\constants\\UserRole.ts": "67", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\types\\errors.ts": "68", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\utils\\errorHandler.ts": "69", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\BaseService.ts": "70"}, {"size": 723, "mtime": 1748491419417, "results": "71", "hashOfConfig": "72"}, {"size": 5755, "mtime": 1748507028476, "results": "73", "hashOfConfig": "72"}, {"size": 101, "mtime": 1745066705345, "results": "74", "hashOfConfig": "72"}, {"size": 2211, "mtime": 1745035741074, "results": "75", "hashOfConfig": "72"}, {"size": 86, "mtime": 1748332967806, "results": "76", "hashOfConfig": "72"}, {"size": 169, "mtime": 1744775143109, "results": "77", "hashOfConfig": "72"}, {"size": 35, "mtime": 1744425056957, "results": "78", "hashOfConfig": "72"}, {"size": 30, "mtime": 1744425079330, "results": "79", "hashOfConfig": "72"}, {"size": 15199, "mtime": 1745077672426, "results": "80", "hashOfConfig": "72"}, {"size": 18233, "mtime": 1745077721335, "results": "81", "hashOfConfig": "72"}, {"size": 4695, "mtime": 1748314142037, "results": "82", "hashOfConfig": "72"}, {"size": 6728, "mtime": 1748492211973, "results": "83", "hashOfConfig": "72"}, {"size": 11015, "mtime": 1745069551218, "results": "84", "hashOfConfig": "72"}, {"size": 413, "mtime": 1745075910506, "results": "85", "hashOfConfig": "72"}, {"size": 34467, "mtime": 1748340458306, "results": "86", "hashOfConfig": "72"}, {"size": 8031, "mtime": 1748314142044, "results": "87", "hashOfConfig": "72"}, {"size": 2828, "mtime": 1745308751151, "results": "88", "hashOfConfig": "72"}, {"size": 1581, "mtime": 1748487712747, "results": "89", "hashOfConfig": "72"}, {"size": 1928, "mtime": 1748487766246, "results": "90", "hashOfConfig": "72"}, {"size": 1094, "mtime": 1748487781921, "results": "91", "hashOfConfig": "72"}, {"size": 1686, "mtime": 1748314142057, "results": "92", "hashOfConfig": "72"}, {"size": 4903, "mtime": 1745069551220, "results": "93", "hashOfConfig": "72"}, {"size": 3108, "mtime": 1748487799761, "results": "94", "hashOfConfig": "72"}, {"size": 272, "mtime": 1745034200281, "results": "95", "hashOfConfig": "72"}, {"size": 7346, "mtime": 1748314142058, "results": "96", "hashOfConfig": "72"}, {"size": 921, "mtime": 1748487863358, "results": "97", "hashOfConfig": "72"}, {"size": 151, "mtime": 1745308751155, "results": "98", "hashOfConfig": "72"}, {"size": 4707, "mtime": 1748333692174, "results": "99", "hashOfConfig": "72"}, {"size": 13826, "mtime": 1748491998248, "results": "100", "hashOfConfig": "72"}, {"size": 4897, "mtime": 1748492244838, "results": "101", "hashOfConfig": "72"}, {"size": 270, "mtime": 1748491419565, "results": "102", "hashOfConfig": "72"}, {"size": 4552, "mtime": 1748487676062, "results": "103", "hashOfConfig": "72"}, {"size": 2756, "mtime": 1748487595669, "results": "104", "hashOfConfig": "72"}, {"size": 4205, "mtime": 1748487621095, "results": "105", "hashOfConfig": "72"}, {"size": 1648, "mtime": 1748487694524, "results": "106", "hashOfConfig": "72"}, {"size": 2316, "mtime": 1745308751152, "results": "107", "hashOfConfig": "72"}, {"size": 1852, "mtime": 1745308751152, "results": "108", "hashOfConfig": "72"}, {"size": 1203, "mtime": 1745308751153, "results": "109", "hashOfConfig": "72"}, {"size": 6361, "mtime": 1748314142057, "results": "110", "hashOfConfig": "72"}, {"size": 7136, "mtime": 1748487728617, "results": "111", "hashOfConfig": "72"}, {"size": 13045, "mtime": 1748487746428, "results": "112", "hashOfConfig": "72"}, {"size": 1892, "mtime": 1748487818317, "results": "113", "hashOfConfig": "72"}, {"size": 1264, "mtime": 1745308751154, "results": "114", "hashOfConfig": "72"}, {"size": 510, "mtime": 1748491419790, "results": "115", "hashOfConfig": "72"}, {"size": 2185, "mtime": 1748314142058, "results": "116", "hashOfConfig": "72"}, {"size": 4364, "mtime": 1748487879447, "results": "117", "hashOfConfig": "72"}, {"size": 3076, "mtime": 1748016415198, "results": "118", "hashOfConfig": "72"}, {"size": 3945, "mtime": 1748016415217, "results": "119", "hashOfConfig": "72"}, {"size": 818, "mtime": 1748491419842, "results": "120", "hashOfConfig": "72"}, {"size": 157, "mtime": 1748333061068, "results": "121", "hashOfConfig": "72"}, {"size": 790, "mtime": 1748491419850, "results": "122", "hashOfConfig": "72"}, {"size": 267, "mtime": 1748491419861, "results": "123", "hashOfConfig": "72"}, {"size": 667, "mtime": 1748314142060, "results": "124", "hashOfConfig": "72"}, {"size": 4715, "mtime": 1748509735716, "results": "125", "hashOfConfig": "72"}, {"size": 1465, "mtime": 1748509650521, "results": "126", "hashOfConfig": "72"}, {"size": 3859, "mtime": 1748509816598, "results": "127", "hashOfConfig": "72"}, {"size": 404, "mtime": 1748314142062, "results": "128", "hashOfConfig": "72"}, {"size": 2042, "mtime": 1748491419910, "results": "129", "hashOfConfig": "72"}, {"size": 766, "mtime": 1748491419916, "results": "130", "hashOfConfig": "72"}, {"size": 11727, "mtime": 1748506896753, "results": "131", "hashOfConfig": "72"}, {"size": 1453, "mtime": 1748489012670, "results": "132", "hashOfConfig": "72"}, {"size": 543, "mtime": 1748491419956, "results": "133", "hashOfConfig": "72"}, {"size": 4229, "mtime": 1748492839220, "results": "134", "hashOfConfig": "72"}, {"size": 1136, "mtime": 1748491419976, "results": "135", "hashOfConfig": "72"}, {"size": 11397, "mtime": 1748490184974, "results": "136", "hashOfConfig": "72"}, {"size": 1350, "mtime": 1748491420031, "results": "137", "hashOfConfig": "72"}, {"size": 125, "mtime": 1748490772644, "results": "138", "hashOfConfig": "72"}, {"size": 3685, "mtime": 1748491139818, "results": "139", "hashOfConfig": "72"}, {"size": 3950, "mtime": 1748491343415, "results": "140", "hashOfConfig": "72"}, {"size": 6064, "mtime": 1748509628479, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1erbh3i", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\layout.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\index.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\button.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\constants\\index.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\utils.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\index.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\index.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\admin\\payments\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\admin\\payments\\[id]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\[eventId]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\event-detail\\[id]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\orders\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\event\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\event\\[id]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\ConfirmPopup.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\SeatOrderCard.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\event-detail\\InfoNavigator.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\Header.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\home\\EventInfo.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\orders\\OrderCard.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\dropdown.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\fonts.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\place-holder.data.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Event.ts", [], ["352", "353", "354", "355", "356", "357", "358", "359", "360", "361"], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Seat.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\auth\\[slug]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\payment\\[eventId]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\orders\\[id]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\provider.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\ForgotPasswordForm.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\LoginForm.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\SignUpForm.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\SeatMap.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TicketCard.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TimeCount.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TimeInfoConfirmPopup.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\venue-data.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\VenueMapSVG.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\VenueSectionSVG.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\loading.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\textinput.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\redux.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useBooking.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useEvents.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useUser.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useVenue.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\EventDTO.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\UserDTO.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\VenueDTO.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\User.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Venue.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\eventService.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\userService.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\venueService.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\booking\\bookingSelector.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\booking\\bookingSlice.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\event\\eventSelector.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\event\\eventSlice.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\store.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\user\\userSelector.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\user\\userSlice.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\venue\\venueSelector.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\venue\\venueSlice.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\utils\\seatConverter.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\constants\\UserRole.ts", [], ["362", "363", "364"], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\types\\errors.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\utils\\errorHandler.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\BaseService.ts", [], [], {"ruleId": "365", "severity": 1, "message": "366", "line": 29, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 29, "endColumn": 10, "suggestions": "369", "suppressions": "370"}, {"ruleId": "365", "severity": 1, "message": "371", "line": 30, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 30, "endColumn": 8, "suggestions": "372", "suppressions": "373"}, {"ruleId": "365", "severity": 1, "message": "374", "line": 31, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 31, "endColumn": 9, "suggestions": "375", "suppressions": "376"}, {"ruleId": "365", "severity": 1, "message": "377", "line": 35, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 35, "endColumn": 8, "suggestions": "378", "suppressions": "379"}, {"ruleId": "365", "severity": 1, "message": "380", "line": 36, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 36, "endColumn": 22, "suggestions": "381", "suppressions": "382"}, {"ruleId": "365", "severity": 1, "message": "383", "line": 37, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 37, "endColumn": 12, "suggestions": "384", "suppressions": "385"}, {"ruleId": "365", "severity": 1, "message": "386", "line": 38, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 38, "endColumn": 12, "suggestions": "387", "suppressions": "388"}, {"ruleId": "365", "severity": 1, "message": "389", "line": 39, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 39, "endColumn": 14, "suggestions": "390", "suppressions": "391"}, {"ruleId": "365", "severity": 1, "message": "392", "line": 40, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 40, "endColumn": 11, "suggestions": "393", "suppressions": "394"}, {"ruleId": "365", "severity": 1, "message": "395", "line": 41, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 41, "endColumn": 11, "suggestions": "396", "suppressions": "397"}, {"ruleId": "365", "severity": 1, "message": "398", "line": 3, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 3, "endColumn": 8, "suggestions": "399", "suppressions": "400"}, {"ruleId": "365", "severity": 1, "message": "401", "line": 4, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 4, "endColumn": 7, "suggestions": "402", "suppressions": "403"}, {"ruleId": "365", "severity": 1, "message": "404", "line": 5, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 5, "endColumn": 12, "suggestions": "405", "suppressions": "406"}, "no-unused-vars", "'CONCERT' is defined but never used.", "Identifier", "unusedVar", ["407"], ["408"], "'MATCH' is defined but never used.", ["409"], ["410"], "'OTHERS' is defined but never used.", ["411"], ["412"], "'DRAFT' is defined but never used.", ["413"], ["414"], "'SUBMIT_FOR_APPROVAL' is defined but never used.", ["415"], ["416"], "'PUBLISHED' is defined but never used.", ["417"], ["418"], "'POSTPONED' is defined but never used.", ["419"], ["420"], "'RESCHEDULED' is defined but never used.", ["421"], ["422"], "'CANCELED' is defined but never used.", ["423"], ["424"], "'REJECTED' is defined but never used.", ["425"], ["426"], "'ADMIN' is defined but never used.", ["427"], ["428"], "'USER' is defined but never used.", ["429"], ["430"], "'ORGANIZER' is defined but never used.", ["431"], ["432"], {"messageId": "433", "data": "434", "fix": "435", "desc": "436"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "439", "fix": "440", "desc": "441"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "442", "fix": "443", "desc": "444"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "445", "fix": "446", "desc": "447"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "448", "fix": "449", "desc": "450"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "451", "fix": "452", "desc": "453"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "454", "fix": "455", "desc": "456"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "457", "fix": "458", "desc": "459"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "460", "fix": "461", "desc": "462"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "463", "fix": "464", "desc": "465"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "466", "fix": "467", "desc": "468"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "469", "fix": "470", "desc": "471"}, {"kind": "437", "justification": "438"}, {"messageId": "433", "data": "472", "fix": "473", "desc": "474"}, {"kind": "437", "justification": "438"}, "removeVar", {"varName": "475"}, {"range": "476", "text": "438"}, "Remove unused variable 'CONCERT'.", "directive", "", {"varName": "477"}, {"range": "478", "text": "438"}, "Remove unused variable 'MATCH'.", {"varName": "479"}, {"range": "480", "text": "438"}, "Remove unused variable 'OTHERS'.", {"varName": "481"}, {"range": "482", "text": "438"}, "Remove unused variable 'DRAFT'.", {"varName": "483"}, {"range": "484", "text": "438"}, "Remove unused variable 'SUBMIT_FOR_APPROVAL'.", {"varName": "485"}, {"range": "486", "text": "438"}, "Remove unused variable 'PUBLISHED'.", {"varName": "487"}, {"range": "488", "text": "438"}, "Remove unused variable 'POSTPONED'.", {"varName": "489"}, {"range": "490", "text": "438"}, "Remove unused variable 'RESCHEDULED'.", {"varName": "491"}, {"range": "492", "text": "438"}, "Remove unused variable 'CANCELED'.", {"varName": "493"}, {"range": "494", "text": "438"}, "Remove unused variable 'REJECTED'.", {"varName": "495"}, {"range": "496", "text": "438"}, "Remove unused variable 'ADMIN'.", {"varName": "497"}, {"range": "498", "text": "438"}, "Remove unused variable 'USER'.", {"varName": "499"}, {"range": "500", "text": "438"}, "Remove unused variable 'ORGANI<PERSON><PERSON>'.", "CONCERT", [589, 596], "MATCH", [608, 618], "OTHERS", [628, 639], "DRAFT", [687, 692], "SUBMIT_FOR_APPROVAL", [702, 726], "PUBLISHED", [750, 764], "POSTPONED", [778, 792], "RESCHEDULED", [806, 822], "CANCELED", [838, 851], "REJECTED", [864, 877], "ADMIN", [63, 69], "USER", [68, 77], "ORGANIZER", [77, 91]]