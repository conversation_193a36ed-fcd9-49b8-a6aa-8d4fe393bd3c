[{"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\layout.tsx": "1", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\page.tsx": "2", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\index.ts": "3", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\button.tsx": "4", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\constants\\index.ts": "5", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\utils.ts": "6", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\index.ts": "7", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\index.ts": "8", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\admin\\payments\\page.tsx": "9", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\admin\\payments\\[id]\\page.tsx": "10", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\[eventId]\\page.tsx": "11", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\event-detail\\[id]\\page.tsx": "12", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\orders\\page.tsx": "13", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\event\\page.tsx": "14", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\event\\[id]\\page.tsx": "15", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\page.tsx": "16", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\ConfirmPopup.tsx": "17", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\SeatOrderCard.tsx": "18", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\event-detail\\InfoNavigator.tsx": "19", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\Header.tsx": "20", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\home\\EventInfo.tsx": "21", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\orders\\OrderCard.tsx": "22", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\dropdown.tsx": "23", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\fonts.ts": "24", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\place-holder.data.ts": "25", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Event.ts": "26", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Seat.ts": "27", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\auth\\[slug]\\page.tsx": "28", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\payment\\[eventId]\\page.tsx": "29", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\orders\\[id]\\page.tsx": "30", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\provider.tsx": "31", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\ForgotPasswordForm.tsx": "32", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\LoginForm.tsx": "33", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\SignUpForm.tsx": "34", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\SeatMap.tsx": "35", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TicketCard.tsx": "36", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TimeCount.tsx": "37", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TimeInfoConfirmPopup.tsx": "38", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\venue-data.ts": "39", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\VenueMapSVG.tsx": "40", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\VenueSectionSVG.tsx": "41", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\loading.tsx": "42", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\textinput.tsx": "43", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\redux.ts": "44", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useBooking.ts": "45", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useEvents.ts": "46", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useUser.ts": "47", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useVenue.ts": "48", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\EventDTO.ts": "49", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\UserDTO.ts": "50", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\VenueDTO.ts": "51", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\User.ts": "52", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Venue.ts": "53", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\eventService.ts": "54", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\userService.ts": "55", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\venueService.ts": "56", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\booking\\bookingSelector.ts": "57", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\booking\\bookingSlice.ts": "58", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\event\\eventSelector.ts": "59", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\event\\eventSlice.ts": "60", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\store.ts": "61", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\user\\userSelector.ts": "62", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\user\\userSlice.ts": "63", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\venue\\venueSelector.ts": "64", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\venue\\venueSlice.ts": "65", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\utils\\seatConverter.ts": "66", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\constants\\UserRole.ts": "67", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\types\\errors.ts": "68", "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\utils\\errorHandler.ts": "69"}, {"size": 723, "mtime": 1748491419417, "results": "70", "hashOfConfig": "71"}, {"size": 5808, "mtime": 1748317869694, "results": "72", "hashOfConfig": "71"}, {"size": 101, "mtime": 1745066705345, "results": "73", "hashOfConfig": "71"}, {"size": 2211, "mtime": 1745035741074, "results": "74", "hashOfConfig": "71"}, {"size": 86, "mtime": 1748332967806, "results": "75", "hashOfConfig": "71"}, {"size": 169, "mtime": 1744775143109, "results": "76", "hashOfConfig": "71"}, {"size": 35, "mtime": 1744425056957, "results": "77", "hashOfConfig": "71"}, {"size": 30, "mtime": 1744425079330, "results": "78", "hashOfConfig": "71"}, {"size": 15199, "mtime": 1745077672426, "results": "79", "hashOfConfig": "71"}, {"size": 18233, "mtime": 1745077721335, "results": "80", "hashOfConfig": "71"}, {"size": 4695, "mtime": 1748314142037, "results": "81", "hashOfConfig": "71"}, {"size": 6728, "mtime": 1748492211973, "results": "82", "hashOfConfig": "71"}, {"size": 11015, "mtime": 1745069551218, "results": "83", "hashOfConfig": "71"}, {"size": 413, "mtime": 1745075910506, "results": "84", "hashOfConfig": "71"}, {"size": 34467, "mtime": 1748340458306, "results": "85", "hashOfConfig": "71"}, {"size": 8031, "mtime": 1748314142044, "results": "86", "hashOfConfig": "71"}, {"size": 2828, "mtime": 1745308751151, "results": "87", "hashOfConfig": "71"}, {"size": 1581, "mtime": 1748487712747, "results": "88", "hashOfConfig": "71"}, {"size": 1928, "mtime": 1748487766246, "results": "89", "hashOfConfig": "71"}, {"size": 1094, "mtime": 1748487781921, "results": "90", "hashOfConfig": "71"}, {"size": 1686, "mtime": 1748314142057, "results": "91", "hashOfConfig": "71"}, {"size": 4903, "mtime": 1745069551220, "results": "92", "hashOfConfig": "71"}, {"size": 3108, "mtime": 1748487799761, "results": "93", "hashOfConfig": "71"}, {"size": 272, "mtime": 1745034200281, "results": "94", "hashOfConfig": "71"}, {"size": 7346, "mtime": 1748314142058, "results": "95", "hashOfConfig": "71"}, {"size": 921, "mtime": 1748487863358, "results": "96", "hashOfConfig": "71"}, {"size": 151, "mtime": 1745308751155, "results": "97", "hashOfConfig": "71"}, {"size": 4707, "mtime": 1748333692174, "results": "98", "hashOfConfig": "71"}, {"size": 13826, "mtime": 1748491998248, "results": "99", "hashOfConfig": "71"}, {"size": 4897, "mtime": 1748492244838, "results": "100", "hashOfConfig": "71"}, {"size": 270, "mtime": 1748491419565, "results": "101", "hashOfConfig": "71"}, {"size": 4552, "mtime": 1748487676062, "results": "102", "hashOfConfig": "71"}, {"size": 2756, "mtime": 1748487595669, "results": "103", "hashOfConfig": "71"}, {"size": 4205, "mtime": 1748487621095, "results": "104", "hashOfConfig": "71"}, {"size": 1648, "mtime": 1748487694524, "results": "105", "hashOfConfig": "71"}, {"size": 2316, "mtime": 1745308751152, "results": "106", "hashOfConfig": "71"}, {"size": 1852, "mtime": 1745308751152, "results": "107", "hashOfConfig": "71"}, {"size": 1203, "mtime": 1745308751153, "results": "108", "hashOfConfig": "71"}, {"size": 6361, "mtime": 1748314142057, "results": "109", "hashOfConfig": "71"}, {"size": 7136, "mtime": 1748487728617, "results": "110", "hashOfConfig": "71"}, {"size": 13045, "mtime": 1748487746428, "results": "111", "hashOfConfig": "71"}, {"size": 1892, "mtime": 1748487818317, "results": "112", "hashOfConfig": "71"}, {"size": 1264, "mtime": 1745308751154, "results": "113", "hashOfConfig": "71"}, {"size": 510, "mtime": 1748491419790, "results": "114", "hashOfConfig": "71"}, {"size": 2185, "mtime": 1748314142058, "results": "115", "hashOfConfig": "71"}, {"size": 4364, "mtime": 1748487879447, "results": "116", "hashOfConfig": "71"}, {"size": 3076, "mtime": 1748016415198, "results": "117", "hashOfConfig": "71"}, {"size": 3945, "mtime": 1748016415217, "results": "118", "hashOfConfig": "71"}, {"size": 818, "mtime": 1748491419842, "results": "119", "hashOfConfig": "71"}, {"size": 157, "mtime": 1748333061068, "results": "120", "hashOfConfig": "71"}, {"size": 790, "mtime": 1748491419850, "results": "121", "hashOfConfig": "71"}, {"size": 267, "mtime": 1748491419861, "results": "122", "hashOfConfig": "71"}, {"size": 667, "mtime": 1748314142060, "results": "123", "hashOfConfig": "71"}, {"size": 4855, "mtime": 1748489924984, "results": "124", "hashOfConfig": "71"}, {"size": 1349, "mtime": 1748489952629, "results": "125", "hashOfConfig": "71"}, {"size": 3928, "mtime": 1748490008628, "results": "126", "hashOfConfig": "71"}, {"size": 404, "mtime": 1748314142062, "results": "127", "hashOfConfig": "71"}, {"size": 2042, "mtime": 1748491419910, "results": "128", "hashOfConfig": "71"}, {"size": 766, "mtime": 1748491419916, "results": "129", "hashOfConfig": "71"}, {"size": 11697, "mtime": 1748490102186, "results": "130", "hashOfConfig": "71"}, {"size": 1453, "mtime": 1748489012670, "results": "131", "hashOfConfig": "71"}, {"size": 543, "mtime": 1748491419956, "results": "132", "hashOfConfig": "71"}, {"size": 4331, "mtime": 1748490133648, "results": "133", "hashOfConfig": "71"}, {"size": 1136, "mtime": 1748491419976, "results": "134", "hashOfConfig": "71"}, {"size": 11397, "mtime": 1748490184974, "results": "135", "hashOfConfig": "71"}, {"size": 1350, "mtime": 1748491420031, "results": "136", "hashOfConfig": "71"}, {"size": 125, "mtime": 1748490772644, "results": "137", "hashOfConfig": "71"}, {"size": 3685, "mtime": 1748491139818, "results": "138", "hashOfConfig": "71"}, {"size": 3950, "mtime": 1748491343415, "results": "139", "hashOfConfig": "71"}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1erbh3i", {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\layout.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\index.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\button.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\constants\\index.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\utils.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\index.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\index.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\admin\\payments\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\admin\\payments\\[id]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\[eventId]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\event-detail\\[id]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\orders\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\event\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\event\\[id]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\ConfirmPopup.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\SeatOrderCard.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\event-detail\\InfoNavigator.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\Header.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\home\\EventInfo.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\orders\\OrderCard.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\dropdown.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\fonts.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\libs\\place-holder.data.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Event.ts", [], ["347", "348", "349", "350", "351", "352", "353", "354", "355", "356"], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Seat.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\auth\\[slug]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\booking\\payment\\[eventId]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\orders\\[id]\\page.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\provider.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\ForgotPasswordForm.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\LoginForm.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\auth\\SignUpForm.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\SeatMap.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TicketCard.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TimeCount.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\TimeInfoConfirmPopup.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\venue-data.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\VenueMapSVG.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\booking\\VenueSectionSVG.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\loading.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\components\\ui\\textinput.tsx", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\redux.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useBooking.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useEvents.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useUser.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\hooks\\useVenue.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\EventDTO.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\UserDTO.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\DTO\\VenueDTO.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\User.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\models\\Venue.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\eventService.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\userService.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\services\\venueService.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\booking\\bookingSelector.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\booking\\bookingSlice.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\event\\eventSelector.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\event\\eventSlice.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\store.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\user\\userSelector.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\user\\userSlice.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\venue\\venueSelector.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\store\\venue\\venueSlice.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\utils\\seatConverter.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\constants\\UserRole.ts", [], ["357", "358", "359"], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\types\\errors.ts", [], [], "D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\utils\\errorHandler.ts", [], [], {"ruleId": "360", "severity": 1, "message": "361", "line": 29, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 29, "endColumn": 10, "suggestions": "364", "suppressions": "365"}, {"ruleId": "360", "severity": 1, "message": "366", "line": 30, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 30, "endColumn": 8, "suggestions": "367", "suppressions": "368"}, {"ruleId": "360", "severity": 1, "message": "369", "line": 31, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 31, "endColumn": 9, "suggestions": "370", "suppressions": "371"}, {"ruleId": "360", "severity": 1, "message": "372", "line": 35, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 35, "endColumn": 8, "suggestions": "373", "suppressions": "374"}, {"ruleId": "360", "severity": 1, "message": "375", "line": 36, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 36, "endColumn": 22, "suggestions": "376", "suppressions": "377"}, {"ruleId": "360", "severity": 1, "message": "378", "line": 37, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 37, "endColumn": 12, "suggestions": "379", "suppressions": "380"}, {"ruleId": "360", "severity": 1, "message": "381", "line": 38, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 38, "endColumn": 12, "suggestions": "382", "suppressions": "383"}, {"ruleId": "360", "severity": 1, "message": "384", "line": 39, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 39, "endColumn": 14, "suggestions": "385", "suppressions": "386"}, {"ruleId": "360", "severity": 1, "message": "387", "line": 40, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 40, "endColumn": 11, "suggestions": "388", "suppressions": "389"}, {"ruleId": "360", "severity": 1, "message": "390", "line": 41, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 41, "endColumn": 11, "suggestions": "391", "suppressions": "392"}, {"ruleId": "360", "severity": 1, "message": "393", "line": 3, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 3, "endColumn": 8, "suggestions": "394", "suppressions": "395"}, {"ruleId": "360", "severity": 1, "message": "396", "line": 4, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 4, "endColumn": 7, "suggestions": "397", "suppressions": "398"}, {"ruleId": "360", "severity": 1, "message": "399", "line": 5, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 5, "endColumn": 12, "suggestions": "400", "suppressions": "401"}, "no-unused-vars", "'CONCERT' is defined but never used.", "Identifier", "unusedVar", ["402"], ["403"], "'MATCH' is defined but never used.", ["404"], ["405"], "'OTHERS' is defined but never used.", ["406"], ["407"], "'DRAFT' is defined but never used.", ["408"], ["409"], "'SUBMIT_FOR_APPROVAL' is defined but never used.", ["410"], ["411"], "'PUBLISHED' is defined but never used.", ["412"], ["413"], "'POSTPONED' is defined but never used.", ["414"], ["415"], "'RESCHEDULED' is defined but never used.", ["416"], ["417"], "'CANCELED' is defined but never used.", ["418"], ["419"], "'REJECTED' is defined but never used.", ["420"], ["421"], "'ADMIN' is defined but never used.", ["422"], ["423"], "'USER' is defined but never used.", ["424"], ["425"], "'ORGANIZER' is defined but never used.", ["426"], ["427"], {"messageId": "428", "data": "429", "fix": "430", "desc": "431"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "434", "fix": "435", "desc": "436"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "437", "fix": "438", "desc": "439"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "440", "fix": "441", "desc": "442"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "443", "fix": "444", "desc": "445"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "446", "fix": "447", "desc": "448"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "449", "fix": "450", "desc": "451"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "452", "fix": "453", "desc": "454"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "455", "fix": "456", "desc": "457"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "458", "fix": "459", "desc": "460"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "461", "fix": "462", "desc": "463"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "464", "fix": "465", "desc": "466"}, {"kind": "432", "justification": "433"}, {"messageId": "428", "data": "467", "fix": "468", "desc": "469"}, {"kind": "432", "justification": "433"}, "removeVar", {"varName": "470"}, {"range": "471", "text": "433"}, "Remove unused variable 'CONCERT'.", "directive", "", {"varName": "472"}, {"range": "473", "text": "433"}, "Remove unused variable 'MATCH'.", {"varName": "474"}, {"range": "475", "text": "433"}, "Remove unused variable 'OTHERS'.", {"varName": "476"}, {"range": "477", "text": "433"}, "Remove unused variable 'DRAFT'.", {"varName": "478"}, {"range": "479", "text": "433"}, "Remove unused variable 'SUBMIT_FOR_APPROVAL'.", {"varName": "480"}, {"range": "481", "text": "433"}, "Remove unused variable 'PUBLISHED'.", {"varName": "482"}, {"range": "483", "text": "433"}, "Remove unused variable 'POSTPONED'.", {"varName": "484"}, {"range": "485", "text": "433"}, "Remove unused variable 'RESCHEDULED'.", {"varName": "486"}, {"range": "487", "text": "433"}, "Remove unused variable 'CANCELED'.", {"varName": "488"}, {"range": "489", "text": "433"}, "Remove unused variable 'REJECTED'.", {"varName": "490"}, {"range": "491", "text": "433"}, "Remove unused variable 'ADMIN'.", {"varName": "492"}, {"range": "493", "text": "433"}, "Remove unused variable 'USER'.", {"varName": "494"}, {"range": "495", "text": "433"}, "Remove unused variable 'ORGANI<PERSON><PERSON>'.", "CONCERT", [589, 596], "MATCH", [608, 618], "OTHERS", [628, 639], "DRAFT", [687, 692], "SUBMIT_FOR_APPROVAL", [702, 726], "PUBLISHED", [750, 764], "POSTPONED", [778, 792], "RESCHEDULED", [806, 822], "CANCELED", [838, 851], "REJECTED", [864, 877], "ADMIN", [63, 69], "USER", [68, 77], "ORGANIZER", [77, 91]]