import { User } from "@/models/User";
import authService from "@/services/authService";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ErrorHandler } from "@/utils/errorHandler";
import { LoginDTO, SignupDTO } from "@/models/DTO/AuthDTO";

interface UserState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  forgotPasswordStatus: "idle" | "loading" | "succeeded" | "failed";
  forgotPasswordError: string | null;
}

const initialState: UserState = {
  user: null,
  isLoading: false,
  error: null,
  isAuthenticated: false,
  forgotPasswordStatus: "idle",
  forgotPasswordError: null,
};

export const loginUser = createAsyncThunk(
  "user/login",
  async (credentials: LoginDTO, { rejectWithValue }) => {
    try {
      const response = await authService.login(credentials);
      return response;
    } catch (error) {
      return rejectWithValue(ErrorHandler.handleAsyncThunkErrorFromCatch(error));
    }
  }
);

export const signupUser = createAsyncThunk(
  "auth/signup",
  async (userData: SignupDTO, { rejectWithValue }) => {
    try {
      const data = await authService.signup(userData);
      return data as User;
    } catch (error) {
      return rejectWithValue(ErrorHandler.handleAsyncThunkErrorFromCatch(error));
    }
  }
);

export const requestPasswordReset = createAsyncThunk(
  "auth/forgotPassword",
  async (email: string, { rejectWithValue }) => {
    try {
      const data = await authService.forgotPassword(email);
      return data;
    } catch (error) {
      return rejectWithValue(ErrorHandler.handleAsyncThunkErrorFromCatch(error));
    }
  }
);

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    logout: state => {
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
      // localStorage.removeItem('user'); // Clear local storage
    },
    // Reset forgot password state
    resetForgotPasswordState: state => {
      state.forgotPasswordStatus = "idle";
      state.forgotPasswordError = null;
    },
  },
  extraReducers: builder => {
    builder
      // Handle login thunk
      .addCase(loginUser.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action: PayloadAction<User>) => {
        state.isLoading = false;

      
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.error = (action.payload as string) || "Login failed";
      })
      .addCase(signupUser.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(signupUser.fulfilled, (state, action: PayloadAction<User>) => {
        state.isLoading = false;
        state.error = null; // Clear any previous errors
        state.user = action.payload;
      })
      .addCase(signupUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as string) || "Signup failed";
      })
      // Handle forgot password thunk
      .addCase(requestPasswordReset.pending, state => {
        state.forgotPasswordStatus = "loading";
        state.forgotPasswordError = null;
      })
      .addCase(requestPasswordReset.fulfilled, state => {
        state.forgotPasswordStatus = "succeeded";
        state.forgotPasswordError = null;
      })
      .addCase(requestPasswordReset.rejected, (state, action) => {
        state.forgotPasswordStatus = "failed";
        state.forgotPasswordError = (action.payload as string) || "Failed to send reset link";
      });
  },
});

export const { logout, resetForgotPasswordState } = userSlice.actions;

export default userSlice.reducer;
