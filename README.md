# Ticket Selling Platform

A web application for buying and selling tickets to events.

## Project Structure

This repository contains the following components:

- `client/`: Frontend application built with Next.js

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd project
   ```

2. Install dependencies for the client:
   ```bash
   cd client
   npm install
   # or
   yarn install
   ```

### Running the Application

#### Client (Frontend)

```bash
cd client
npm run dev
# or
yarn dev
```

The client application will be available at [http://localhost:3000](http://localhost:3000).
