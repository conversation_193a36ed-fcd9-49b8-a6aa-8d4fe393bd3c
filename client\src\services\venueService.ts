import BaseService from "./BaseService";
import { UpdateVenueDTO } from "@/models/DTO/VenueDTO";
import { CreateSectionDTO, CreateVenueDTO, UpdateSectionDTO } from "@/models/DTO/VenueDTO";
import { Section, Venue } from "@/models/Venue";
import { ErrorHandler } from "@/utils/errorHandler";
import {
  ApiVenue,
  ApiSection,
  ApiSeat,
  CreateVenueRequest,
  UpdateVenueRequest,
  CreateSectionRequest,
} from "@/types/api";
import { adaptApiVenueToVenue } from "@/utils/apiAdapters";

class VenueService extends BaseService {
  constructor() {
    super("/api/venues", {
      enableAuth: true, // Venue management requires authentication
    });
  }

  async getAllVenues(): Promise<Venue[]> {
    try {
      const response = await this.get<ApiVenue[]>("");
      return response.data.map(apiVenue => adaptApiVenueToVenue(apiVenue));
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Fetch venues");
    }
  }

  async getVenueById(venueId: string): Promise<Venue> {
    try {
      const response = await this.get<ApiVenue>(`/${venueId}`);
      return adaptApiVenueToVenue(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Fetch venue by ID");
    }
  }

  async createVenue(venueData: CreateVenueDTO): Promise<Venue> {
    try {
      // Convert frontend DTO to backend API request
      const createRequest: CreateVenueRequest = {
        name: venueData.name,
        address: venueData.address,
        city: venueData.city,
        ownerUserId: venueData.ownerUserId,
      };
      const response = await this.post<ApiVenue>("", createRequest);
      return adaptApiVenueToVenue(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Create venue");
    }
  }

  async updateVenue(venueId: string, venueData: UpdateVenueDTO): Promise<Venue> {
    try {
      // Convert frontend DTO to backend API request
      const updateRequest: UpdateVenueRequest = {
        name: venueData.name,
        address: venueData.address,
        city: venueData.city,
      };
      const response = await this.put<ApiVenue>(`/${venueId}`, updateRequest);
      return adaptApiVenueToVenue(response.data);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Update venue (${venueId})`);
    }
  }

  async deleteVenue(venueId: string): Promise<void> {
    try {
      await this.delete(`/${venueId}`);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Delete venue (${venueId})`);
    }
  }

  async getAllSeatsForVenue(venueId: string): Promise<ApiSeat[]> {
    try {
      const response = await this.get<ApiSeat[]>(`/${venueId}/seats`);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Fetch seats for venue (${venueId})`);
    }
  }

  async getAllSectionsForVenue(venueId: string): Promise<Section[]> {
    try {
      const response = await this.get<ApiSection[]>(`/${venueId}/sections`);
      return response.data.map(apiSection => ({
        sectionId: apiSection.sectionId,
        venueId: apiSection.venueId,
        name: apiSection.name,
        capacity: apiSection.capacity,
        seats:
          apiSection.seats?.map(seat => ({
            seatId: seat.seatId,
            sectionId: seat.sectionId,
            seatNumber: seat.seatNumber,
            rowNumber: seat.rowNumber,
            seatInRow: seat.seatInRow,
            status: "available" as const,
          })) || [],
      }));
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Fetch sections for venue (${venueId})`);
    }
  }

  async getSectionById(venueId: string, sectionId: string): Promise<Section> {
    try {
      const response = await this.get<ApiSection>(`/${venueId}/sections/${sectionId}`);
      const apiSection = response.data;
      return {
        sectionId: apiSection.sectionId,
        venueId: apiSection.venueId,
        name: apiSection.name,
        capacity: apiSection.capacity,
        seats:
          apiSection.seats?.map(seat => ({
            seatId: seat.seatId,
            sectionId: seat.sectionId,
            seatNumber: seat.seatNumber,
            rowNumber: seat.rowNumber,
            seatInRow: seat.seatInRow,
            status: "available" as const,
          })) || [],
      };
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(
        error,
        `Fetch section (${sectionId}) for venue (${venueId})`
      );
    }
  }

  async createSection(venueId: string, sectionData: CreateSectionDTO): Promise<Section> {
    try {
      // Convert frontend DTO to backend API request
      const createRequest: CreateSectionRequest = {
        name: sectionData.name,
        capacity: sectionData.capacity,
        seats: sectionData.seats.map(seat => ({
          seatNumber: seat.seatNumber,
          rowNumber: seat.rowNumber,
          seatInRow: seat.seatInRow,
        })),
      };
      const response = await this.post<ApiSection>(`/${venueId}/sections`, createRequest);
      const apiSection = response.data;
      return {
        sectionId: apiSection.sectionId,
        venueId: apiSection.venueId,
        name: apiSection.name,
        capacity: apiSection.capacity,
        seats:
          apiSection.seats?.map(seat => ({
            seatId: seat.seatId,
            sectionId: seat.sectionId,
            seatNumber: seat.seatNumber,
            rowNumber: seat.rowNumber,
            seatInRow: seat.seatInRow,
            status: "available" as const,
          })) || [],
      };
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Create section for venue (${venueId})`);
    }
  }

  async updateSection(
    venueId: string,
    sectionId: string,
    sectionData: UpdateSectionDTO
  ): Promise<Section> {
    try {
      // Convert frontend DTO to backend API request
      const updateRequest: CreateSectionRequest = {
        name: sectionData.name || "",
        capacity: sectionData.capacity || 0,
        seats: [], // UpdateSectionDTO doesn't include seats, handle separately if needed
      };
      const response = await this.put<ApiSection>(
        `/${venueId}/sections/${sectionId}`,
        updateRequest
      );
      const apiSection = response.data;
      return {
        sectionId: apiSection.sectionId,
        venueId: apiSection.venueId,
        name: apiSection.name,
        capacity: apiSection.capacity,
        seats:
          apiSection.seats?.map(seat => ({
            seatId: seat.seatId,
            sectionId: seat.sectionId,
            seatNumber: seat.seatNumber,
            rowNumber: seat.rowNumber,
            seatInRow: seat.seatInRow,
            status: "available" as const,
          })) || [],
      };
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(
        error,
        `Update section (${sectionId}) for venue (${venueId})`
      );
    }
  }

  async deleteSection(venueId: string, sectionId: string): Promise<void> {
    try {
      await this.delete(`/${venueId}/sections/${sectionId}`);
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(
        error,
        `Delete section (${sectionId}) for venue (${venueId})`
      );
    }
  }
}

const venueService = new VenueService();
export default venueService;
