(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[187],{171:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>H});var a=r(5155),s=r(2115),n=r(285),l=r(6707),i=r(6474),o=r(5196);function c(e){let{options:t,value:r,onChange:n,placeholder:c="Select an option",className:d,buttonClassName:u,menuClassName:m,optionClassName:v}=e,[g,h]=(0,s.useState)(!1),p=(0,s.useRef)(null),x=t.find(e=>e.value===r),f=e=>{n(e),h(!1)};return(0,s.useEffect)(()=>{let e=e=>{p.current&&!p.current.contains(e.target)&&h(!1)};return g&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[g]),(0,a.jsxs)("div",{ref:p,className:(0,l.cn)("relative inline-block w-full",d),children:[(0,a.jsxs)("button",{type:"button",className:(0,l.cn)("flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",u),onClick:()=>{h(!g)},children:[(0,a.jsx)("span",{children:x?x.label:c}),(0,a.jsx)(i.A,{className:"w-4 h-4 ml-2"})]}),g&&(0,a.jsx)("div",{className:(0,l.cn)("absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg",m),children:(0,a.jsx)("ul",{className:"py-1 overflow-auto text-base max-h-60",children:t.map(e=>(0,a.jsx)("li",{children:(0,a.jsxs)("button",{type:"button",className:(0,l.cn)("flex items-center justify-between w-full px-4 py-2 text-sm text-left hover:bg-gray-100",r===e.value&&"bg-primary/10 text-primary",v),onClick:()=>f(e.value),children:[e.label,r===e.value&&(0,a.jsx)(o.A,{className:"w-4 h-4 ml-2"})]})},e.value))})})]})}var d=r(646),u=r(4861),m=r(4516),v=r(2525),g=r(9869),h=r(4616),p=r(6766),x=r(5695),f=r(6874),b=r.n(f),j=r(6783),y=r(7602),w=r(8924);let N=e=>e.venues.venues,E=e=>e.venues.currentVenue,k=(0,w.Mz)([E],e=>(null==e?void 0:e.sections)||[]),S=e=>e.venues.isLoadingList,C=e=>e.venues.isLoadingDetails,D=e=>e.venues.isLoadingSections,T=e=>e.venues.errorList,A=e=>e.venues.errorDetails,P=e=>e.venues.errorSections;var L=r(8710);let R=()=>{let e=(0,L.j)(),t=(0,L.G)(N),r=(0,L.G)(S);return{venues:t,isLoadingList:r,error:(0,L.G)(T),loadVenues:(0,s.useCallback)(()=>e((0,y.Of)()),[e])}},G=()=>{let e=(0,L.j)(),t=(0,L.G)(E),r=(0,L.G)(k),a=(0,L.G)(C),n=(0,L.G)(D),l=(0,L.G)(A),i=(0,L.G)(P),o=(0,s.useCallback)(t=>e((0,y.qT)(t)),[e]);return{venue:t,sections:r,isLoadingDetails:a,isLoadingSections:n,errorDetails:l,errorSections:i,loadVenue:o,loadSections:(0,s.useCallback)(t=>e((0,y.rh)(t)),[e]),clearDetails:(0,s.useCallback)(()=>e((0,y.Mo)()),[e])}};var I=r(5436),O=r(7023),z=r(2376);let M=[{value:"MATCH",label:"Match"},{value:"CONCERT",label:"Concert"},{value:"OTHERS",label:"Others"}],F={Draft:["Submit for approval"],"Submit for approval":["Published"],Published:["Cancelled","Postponed"],Postponed:["Cancelled"],Cancelled:[],Rescheduled:["Cancelled","Postponed"]};function H(){var e;let t=(0,x.useParams)(),r=(0,x.useRouter)(),l=null==t?void 0:t.id,i=l&&"new"!==l,{user:o}=(0,z.Q8)(),{createEvent:f,updateEvent:y,rescheduleEvent:w,cancelEvent:N,approveEvent:E,submitEvent:k,postponeEvent:S,isLoadingEventMuatation:C,clearError:D}=(0,j.Qb)(),{loadEvent:T,isLoadingEventDetails:A,event:P,clearDetails:L}=(0,j.mJ)(),{isLoadingDetails:H}=G(),{venues:_,isLoadingList:V,loadVenues:q}=R(),[U,$]=(0,s.useState)(""),[B,Y]=(0,s.useState)(null),[J,Q]=(0,s.useState)(""),[W,Z]=(0,s.useState)(""),[K,X]=(0,s.useState)(""),[ee,et]=(0,s.useState)(""),[er,ea]=(0,s.useState)(null),[es,en]=(0,s.useState)(null),[el,ei]=(0,s.useState)([]),[eo,ec]=(0,s.useState)(""),[ed,eu]=(0,s.useState)(I.f.DRAFT),[em,ev]=(0,s.useState)(""),[eg,eh]=(0,s.useState)(!1),[ep,ex]=(0,s.useState)([]),[ef,eb]=(0,s.useState)([]),[ej,ey]=(0,s.useState)({startDateTime:"",endDateTime:""}),ew=(0,s.useRef)(null),eN=(0,s.useRef)(null);if((0,s.useEffect)(()=>{q(),i&&l?T(l):(L(),$(""),Y(null),Q(""),Z(""),X(""),et(""),ea(null),en(null),ei([]),ec(""),eu(I.f.DRAFT),eb([]),ey({startDateTime:"",endDateTime:""}))},[T,l,i,L,q]),(0,s.useEffect)(()=>{if(P&&i){$(P.name),Y(P.category),Q(P.description);let e=new Date(P.startDateTime),t=new Date(P.endDateTime);Z(e.toISOString().split("T")[0]),X("".concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0"))),et("".concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))),ey({startDateTime:P.startDateTime.toISOString(),endDateTime:P.endDateTime.toISOString()}),en(P.poster),ei(P.images),ec(P.details),eu(P.status),eb(P.sectionPricing)}},[P,i]),(0,s.useEffect)(()=>{P&&i&&ea(_.find(e=>e.venueId===P.venueId)||null)},[_,P,i]),A||H||V)return(0,a.jsx)(O.A,{});let eE=e=>{ei(el.filter((t,r)=>r!==e))},ek="Draft"!==ed&&"Postponed"!==ed,eS="Draft"!==ed,eC=async e=>{if(e===I.f.REJECTED)return void eh(!0);try{let t;switch(e){case I.f.SUBMIT_FOR_APPROVAL:if(!l)return void alert("Please save the draft event first.");t=await k(l);break;case I.f.CANCELED:if(!l)return;t=await N(l);break;case I.f.POSTPONED:if(!l)return;t=await S(l);break;default:console.warn("Unknown status action:",e);return}t&&(eu(t.status),alert("Event status changed to ".concat(t.status)),await T(l))}catch(r){let t=r instanceof Error?r.message:"Unknown error";console.error("Failed to ".concat(e," event:"),r),alert("Error: ".concat(t))}},eD=async()=>{if(l){if(""===em.trim())return void alert("Please provide a reason for rejection");try{let e=await N(l);eu(e.status),eh(!1),ev("")}catch(t){let e=t instanceof Error?t.message:"Unknown error";console.error("Failed to reject event:",t),alert("Error rejecting event: ".concat(e))}}},eT=async()=>{if(l)try{await E(l),await T(l),eu(I.f.PUBLISHED),alert("Event approved successfully")}catch(e){console.error("Failed to approve event:",e),alert("Error approving event")}},eA=()=>{if(!W||!K||!ee||!ej.startDateTime)return!1;let e=new Date("".concat(W,"T").concat(K,":00Z")).toISOString(),t=new Date("".concat(W,"T").concat(ee,":00Z")).toISOString();return ej.startDateTime!==e||ej.endDateTime!==t},eP=async e=>{if(e.preventDefault(),D(),!B||!(null==er?void 0:er.venueId))return void alert("Please fill in all required fields, including category and selecting a venue.");let t=new Date("".concat(W,"T").concat(K,":00Z")).toISOString(),a=new Date("".concat(W,"T").concat(ee,":00Z")).toISOString();if(i&&l)if("Postponed"===ed&&eA())try{let e=await w(l,{newStartDateTime:K,newEndDateTime:ee});eu(e.status),alert("Event has been rescheduled successfully!"),r.push("/organizer/event")}catch(e){console.error("Failed to reschedule event:",e)}else{let e={name:U,category:B,description:J,venueId:er.venueId,poster:es||"",images:el,details:eo};try{await y(l,e),alert("Event updated successfully!"),r.push("/organizer")}catch(e){console.error("Failed to update event:",e)}}else{let e={name:U,category:B,description:J,startDateTime:t,endDateTime:a,venueId:er.venueId,venueName:er.name,venueAddress:er.address,organizerUserId:(null==o?void 0:o.userId)||"",poster:es||"",images:el,details:eo,sectionPricing:ep};try{await f(e),alert("Event created successfully!"),r.push("/organizer")}catch(e){console.error("Failed to create event:",e)}}},eL=es||"https://images.unsplash.com/photo-1577223625816-7546f13df25d?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D";return(0,a.jsxs)("div",{className:"max-w-[1200px] mx-auto px-20 py-10",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:i?"Edit Event":"Create New Event"}),i&&(0,a.jsxs)("div",{className:"mb-6 p-4 border border-gray-300 rounded-md bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:["Current Status: ",(0,a.jsx)("span",{className:"font-bold",children:ed})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Draft"===ed&&"Your event is in draft mode. You can edit all details.","Submit for approval"===ed&&"Your event is awaiting approval from an admin.","Published"===ed&&"Your event is live. Some fields cannot be edited directly.","Postponed"===ed&&"Your event has been postponed. You can reschedule it.","Canceled"===ed&&"Your event has been cancelled.","Rescheduled"===ed&&"Your event has been rescheduled.","Rejected"===ed&&"Your event has been rejected by an admin."]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:["Submit for approval"===ed&&(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n.$,{onClick:eT,className:"bg-green-500 hover:bg-green-600 text-white flex items-center",children:[(0,a.jsx)(d.A,{className:"mr-1",size:16})," Approve"]}),(0,a.jsxs)(n.$,{onClick:()=>eC(I.f.REJECTED),className:"bg-red-500 hover:bg-red-600 text-white flex items-center",children:[(0,a.jsx)(u.A,{className:"mr-1",size:16})," Reject"]})]}),ed&&(null==(e=F[ed])?void 0:e.map(e=>(0,a.jsx)(n.$,{onClick:()=>eC(e),className:"bg-blue-500 hover:bg-blue-600 text-white disabled:opacity-50",children:e},e)))]})]}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-300",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"font-medium",children:"Admin Actions"}),(0,a.jsx)(b(),{href:"/admin/payments",className:"text-blue-500 hover:text-blue-700",children:"View Payment Transactions"})]})})]}),eg&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full",children:[(0,a.jsx)("h3",{className:"text-xl font-bold mb-4",children:"Reject Event"}),(0,a.jsx)("p",{className:"mb-4",children:"Please provide a reason for rejecting this event:"}),(0,a.jsx)("textarea",{value:em,onChange:e=>ev(e.target.value),className:"w-full h-32 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary mb-4",placeholder:"Rejection reason..."}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(n.$,{type:"button",onClick:()=>eh(!1),className:"bg-gray-300 hover:bg-gray-400 text-gray-800",children:"Cancel"}),(0,a.jsx)(n.$,{type:"button",onClick:eD,className:"bg-red-500 hover:bg-red-600 text-white",children:"Reject Event"})]})]})}),(0,a.jsxs)("form",{onSubmit:eP,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Name"}),(0,a.jsx)("input",{type:"text",placeholder:"Event name",value:U,onChange:e=>$(e.target.value),className:"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Category"}),(0,a.jsx)(c,{options:M,value:B||"",onChange:e=>Y(e),placeholder:"Select category"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Description"}),(0,a.jsx)("textarea",{placeholder:"Event description",value:J,onChange:e=>Q(e.target.value),className:"w-full h-32 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Date"}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("input",{type:"date",value:W,onChange:e=>Z(e.target.value),className:"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary ".concat(ek?"bg-gray-100 cursor-not-allowed":""),disabled:ek,required:!0})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Start time"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{value:K,onChange:e=>X(e.target.value),className:"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary appearance-none ".concat(ek?"bg-gray-100 cursor-not-allowed":""),disabled:ek,required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select time"}),Array.from({length:24}).map((e,t)=>Array.from({length:4}).map((e,r)=>{let s="".concat(t.toString().padStart(2,"0"),":").concat((15*r).toString().padStart(2,"0"));return(0,a.jsx)("option",{value:s,children:s},s)}))]}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"End time"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{value:ee,onChange:e=>et(e.target.value),className:"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary appearance-none ".concat(ek?"bg-gray-100 cursor-not-allowed":""),disabled:ek,required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select time"}),Array.from({length:24}).map((e,t)=>Array.from({length:4}).map((e,r)=>{let s="".concat(t.toString().padStart(2,"0"),":").concat((15*r).toString().padStart(2,"0"));return(0,a.jsx)("option",{value:s,children:s},s)}))]}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Select Venue"}),(0,a.jsx)(c,{options:_.map(e=>({label:e.name,value:e.venueId})),value:(null==er?void 0:er.venueId)||"",onChange:e=>{ea(_.find(t=>t.venueId===e)||null)}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Venue name"}),(0,a.jsx)("input",{type:"text",placeholder:"Venue name",value:(null==er?void 0:er.name)||"",className:"w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary bg-gray-100",required:!0,readOnly:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Venue location"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Venue location",value:(null==er?void 0:er.address)||"",readOnly:!0,className:"w-full h-10 pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary bg-gray-100",required:!0}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,a.jsx)(m.A,{className:"w-4 h-4 text-gray-400"})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Poster"}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"relative w-full max-w-md",children:[es?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.default,{src:es,alt:"Event poster",width:400,height:250,className:"w-full h-64 object-cover rounded-md"}),(0,a.jsx)("button",{type:"button",onClick:()=>en(null),className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full",children:(0,a.jsx)(v.A,{size:16})})]}):(0,a.jsxs)("div",{onClick:()=>{var e;return null==(e=ew.current)?void 0:e.click()},className:"w-full h-64 border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center cursor-pointer hover:border-primary bg-gray-50",children:[(0,a.jsx)(p.default,{src:eL,alt:"Default stadium",width:400,height:250,className:"w-full h-full object-cover rounded-md opacity-30"}),(0,a.jsxs)("div",{className:"absolute flex flex-col items-center justify-center",children:[(0,a.jsx)(g.A,{size:48,className:"text-gray-400 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Click to upload poster image"})]})]}),(0,a.jsx)("input",{type:"file",ref:ew,onChange:e=>{if(e.target.files&&e.target.files[0]){let t=e.target.files[0],r=new FileReader;r.onload=e=>{var t;(null==(t=e.target)?void 0:t.result)&&en("")},r.readAsDataURL(t)}},accept:"image/*",className:"hidden"})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-2",children:(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Price"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"border border-gray-200 rounded-md p-2 bg-gray-50",children:(0,a.jsx)(p.default,{src:"https://static.vecteezy.com/system/resources/previews/009/384/331/original/stadium-seating-plan-template-free-vector.jpg",alt:"Stadium seating map",width:500,height:300,className:"w-full h-auto object-contain"})}),(0,a.jsxs)("div",{className:"border border-gray-300 rounded-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex justify-around bg-primary p-3 font-medium",children:[(0,a.jsx)("div",{children:"Section"}),(0,a.jsx)("div",{children:"Price ($)"})]}),(0,a.jsxs)("div",{className:"max-h-64 overflow-y-auto",children:[null==er?void 0:er.sections.map((e,t)=>{var r;return(0,a.jsxs)("div",{className:"grid grid-cols-2 p-3 border-t border-gray-200",children:[(0,a.jsx)("p",{className:"text-center",children:e.name}),(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:(null==(r=ep[t])?void 0:r.price)||"",onChange:r=>{let a=[...ep];a[t]={sectionId:e.sectionId,price:parseFloat(r.target.value)},ex(a)},className:"w-full h-8 px-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary ".concat(eS?"bg-gray-100 cursor-not-allowed":""),disabled:eS})})]},t)}),!l&&(null==er?void 0:er.sections.map((e,t)=>(0,a.jsxs)("div",{className:"grid grid-cols-2 p-3 border-t border-gray-200",children:[(0,a.jsx)("p",{className:"text-center",children:e.sectionId}),(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:e.price,onChange:e=>{let r=[...ef];r[t].price=parseFloat(e.target.value),eb(r)},className:"w-full h-8 px-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary ".concat(eS?"bg-gray-100 cursor-not-allowed":""),disabled:eS})})]},t)))]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Details"}),(0,a.jsx)("textarea",{placeholder:"Event details",value:eo,onChange:e=>ec(e.target.value),className:"w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Images"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[el.map((e,t)=>(0,a.jsxs)("div",{className:"relative w-32 h-32",children:[(0,a.jsx)(p.default,{src:e,alt:"Gallery image ".concat(t+1),width:128,height:128,className:"w-full h-full object-cover rounded-md"}),(0,a.jsx)("button",{type:"button",onClick:()=>eE(t),className:"absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full",children:(0,a.jsx)(v.A,{size:14})})]},t)),(0,a.jsxs)("div",{onClick:()=>{var e;return null==(e=eN.current)?void 0:e.click()},className:"w-32 h-32 border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center cursor-pointer hover:border-primary",children:[(0,a.jsx)(h.A,{size:24,className:"text-gray-400"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Add image"})]}),(0,a.jsx)("input",{type:"file",ref:eN,onChange:e=>{if(e.target.files&&e.target.files[0]){let t=e.target.files[0],r=new FileReader;r.onload=e=>{var t;(null==(t=e.target)?void 0:t.result)&&ei([...el,""])},r.readAsDataURL(t)}},accept:"image/*",className:"hidden"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,a.jsx)(n.$,{type:"button",onClick:()=>r.push("/organizer"),className:"bg-gray-300 hover:bg-gray-400 text-gray-800 px-6",children:"Cancel"}),(0,a.jsx)(n.$,{type:"submit",className:"bg-green-500 hover:bg-green-600 text-white px-6",disabled:C||"Canceled"===ed,children:C?"Saving...":i?"Update Event":"Create Event"})]})]})]})}},285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var a=r(5155);r(2115);var s=r(4624),n=r(2085),l=r(6707);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-darkText shadow-xs hover:bg-primary/70",outline:"border bg-transparent border-darkStroke shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:n,asChild:o=!1,...c}=e,d=o?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,l.cn)(i({variant:r,size:n,className:t})),...c})}},2119:(e,t,r)=>{Promise.resolve().then(r.bind(r,171))},2376:(e,t,r)=>{"use strict";r.d(t,{Q8:()=>m,G7:()=>h,vz:()=>v,So:()=>g});var a=r(2115),s=r(8710),n=r(4254);let l=e=>e.user.user,i=e=>e.user.isAuthenticated,o=e=>e.user.isLoading,c=e=>e.user.error,d=e=>e.user.forgotPasswordStatus,u=e=>e.user.forgotPasswordError,m=()=>({user:(0,s.G)(l),isAuthenticated:(0,s.G)(i)}),v=()=>{let e=(0,s.j)(),t=(0,s.G)(o),r=(0,s.G)(c),l=(0,s.G)(i);return{login:(0,a.useCallback)(async t=>e((0,n.Lx)(t)).unwrap(),[e]),isLoading:t,error:r,isAuthenticated:l}},g=()=>{let e=(0,s.j)(),t=(0,s.G)(o),r=(0,s.G)(c);return{signup:(0,a.useCallback)(async t=>e((0,n.E_)(t)).unwrap(),[e]),isLoading:t,error:r}},h=()=>{let e=(0,s.j)(),t=(0,s.G)(d),r=(0,s.G)(u);return{request:(0,a.useCallback)(async t=>e((0,n.w2)(t)).unwrap(),[e]),resetState:(0,a.useCallback)(()=>{e((0,n.Ly)())},[e]),status:t,error:r}}},5436:(e,t,r)=>{"use strict";r.d(t,{f:()=>s,h:()=>a});var a=function(e){return e.CONCERT="CONCERT",e.MATCH="MATCH",e.OTHERS="OTHERS",e}({}),s=function(e){return e.DRAFT="Draft",e.SUBMIT_FOR_APPROVAL="Submit for approval",e.PUBLISHED="Published",e.POSTPONED="Postponed",e.RESCHEDULED="Rescheduled",e.CANCELED="Canceled",e.REJECTED="Rejected",e}({})},6707:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},6783:(e,t,r)=>{"use strict";r.d(t,{mJ:()=>h,KC:()=>g,Qb:()=>p});var a=r(2115),s=r(2522);let n=e=>e.events.events,l=e=>e.events.currentEvent,i=e=>e.events.isLoadingList,o=e=>e.events.isLoadingDetails,c=e=>e.events.isLoadingMutation,d=e=>e.events.errorList,u=e=>e.events.errorDetails,m=e=>e.events.errorMutation;var v=r(8710);let g=()=>{let e=(0,v.j)(),t=(0,v.G)(n),r=(0,v.G)(i);return{events:t,isLoadingList:r,errorEventList:(0,v.G)(d),loadEvents:(0,a.useCallback)(()=>e((0,s.fw)()),[e])}},h=()=>{let e=(0,v.j)(),t=(0,v.G)(l),r=(0,v.G)(o),n=(0,v.G)(u);return{event:t,isLoadingEventDetails:r,errorEventDetails:n,loadEvent:(0,a.useCallback)(t=>e((0,s.vR)(t)),[e]),clearDetails:(0,a.useCallback)(()=>{e((0,s.HB)())},[e])}},p=()=>{let e=(0,v.j)(),t=(0,v.G)(c),r=(0,v.G)(m),n=(0,a.useCallback)(t=>e((0,s.lh)(t)).unwrap(),[e]),l=(0,a.useCallback)((t,r)=>e((0,s.qM)({eventId:t,eventData:r})).unwrap(),[e]),i=(0,a.useCallback)(t=>e((0,s.SX)(t)).unwrap(),[e]),o=(0,a.useCallback)((t,r)=>e((0,s.nK)({eventId:t,rescheduleData:r})).unwrap(),[e]),d=(0,a.useCallback)(t=>e((0,s.ls)(t)).unwrap(),[e]),u=(0,a.useCallback)(t=>e((0,s.TL)(t)).unwrap(),[e]),g=(0,a.useCallback)(t=>e((0,s.i$)(t)).unwrap(),[e]);return{isLoadingEventMuatation:t,errorEventMutation:r,createEvent:n,updateEvent:l,removeEvent:i,approveEvent:g,rescheduleEvent:o,postponeEvent:d,cancelEvent:u,submitEvent:(0,a.useCallback)(t=>e((0,s.P_)(t)).unwrap(),[e]),clearError:(0,a.useCallback)(()=>{e((0,s.b9)())},[e])}}},7023:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(5155);r(2115);var s=r(6707);let n=e=>{let{size:t="2.5rem",color:r="primary",thickness:n=4,className:l}=e,i={strokeWidth:n},o="stroke-blue-700";return"primary"===r?o="stroke-primary":"secondary"===r?o="stroke-gray-500":"inherit"===r&&(o="stroke-current"),(0,a.jsxs)("svg",{className:(0,s.cn)("animate-spin",l),style:{width:t,height:t},viewBox:"0 0 50 50",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("circle",{className:(0,s.cn)("opacity-25",o),cx:"25",cy:"25",r:"20",fill:"none",style:i}),(0,a.jsx)("circle",{className:(0,s.cn)("opacity-75",o),cx:"25",cy:"25",r:"20",fill:"none",strokeDasharray:"31.415, 125.66",strokeLinecap:"round",style:i})]})},l=()=>(0,a.jsx)("div",{className:"fixed inset-0 z-50 bg-slate-600 bg-opacity-50 flex items-center justify-center",children:(0,a.jsx)(n,{size:"3rem",color:"primary"})})},8710:(e,t,r)=>{"use strict";r.d(t,{G:()=>n,j:()=>s});var a=r(4540);let s=a.wA.withTypes(),n=a.d4.withTypes()}},e=>{var t=t=>e(e.s=t);e.O(0,[13,63,874,196,254,522,413,441,684,358],()=>t(2119)),_N_E=e.O()}]);