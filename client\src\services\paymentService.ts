import BaseService from "./BaseService";
import { <PERSON>rror<PERSON>and<PERSON> } from "@/utils/errorHandler";
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus 
} from "@/types/api";

class PaymentService extends BaseService {
  constructor() {
    super("/api/payments", {
      enableAuth: true, // Payment management requires authentication
    });
  }

  async initiatePayment(paymentData: PaymentRequest): Promise<PaymentResponse> {
    try {
      const response = await this.post<PaymentResponse>("/initiate", paymentData);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, "Initiate payment");
    }
  }

  async getPaymentStatus(paymentId: string): Promise<PaymentStatus> {
    try {
      const response = await this.get<PaymentStatus>(`/${paymentId}/status`);
      return response.data;
    } catch (error) {
      ErrorHandler.handleServiceErrorFromCatch(error, `Fetch payment status (${paymentId})`);
    }
  }
}

const paymentService = new PaymentService();
export default paymentService;
