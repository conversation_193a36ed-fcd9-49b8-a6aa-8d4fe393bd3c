"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/[slug]/page",{

/***/ "(app-pages-browser)/./src/utils/apiAdapters.ts":
/*!**********************************!*\
  !*** ./src/utils/apiAdapters.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adaptApiEventToEvent: () => (/* binding */ adaptApiEventToEvent),\n/* harmony export */   adaptApiUserToUser: () => (/* binding */ adaptApiUserToUser),\n/* harmony export */   adaptApiVenueToVenue: () => (/* binding */ adaptApiVenueToVenue),\n/* harmony export */   adaptAuthResponseToUser: () => (/* binding */ adaptAuthResponseToUser),\n/* harmony export */   adaptEventToCreateRequest: () => (/* binding */ adaptEventToCreateRequest),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   storeAuthData: () => (/* binding */ storeAuthData)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.ts\");\n// API Response Adapters\n// Convert backend API responses to frontend models\n\n/**\n * Convert AuthResponse to User model for Redux state\n * Note: AuthResponse doesn't contain full user data, so we create a minimal User object\n */ function adaptAuthResponseToUser(authResponse) {\n    return {\n        userId: authResponse.userId || \"\",\n        userName: \"\",\n        email: \"\",\n        firstName: \"\",\n        lastName: \"\",\n        role: _constants__WEBPACK_IMPORTED_MODULE_0__.UserRole.USER\n    };\n}\n/**\n * Convert ApiUser to User model\n */ function adaptApiUserToUser(apiUser) {\n    return {\n        userId: apiUser.id,\n        userName: apiUser.username,\n        email: apiUser.email,\n        password: \"\",\n        firstName: apiUser.firstName,\n        lastName: apiUser.lastName,\n        createdAt: new Date(apiUser.createdAt),\n        updatedAt: new Date()\n    };\n}\n/**\n * Convert ApiEvent to Event model with proper date parsing\n */ function adaptApiEventToEvent(apiEvent) {\n    // Combine date and time strings to create full DateTime\n    const startDateTime = new Date(\"\".concat(apiEvent.date, \"T\").concat(apiEvent.startTime));\n    const endDateTime = new Date(\"\".concat(apiEvent.date, \"T\").concat(apiEvent.endTime));\n    return {\n        eventId: apiEvent.eventId,\n        name: apiEvent.title,\n        description: apiEvent.description || \"\",\n        category: \"Concert\",\n        startDateTime,\n        endDateTime,\n        venueId: apiEvent.venueId,\n        venueName: \"\",\n        venueAddress: \"\",\n        poster: \"\",\n        organizerUserId: apiEvent.organizerId,\n        images: [],\n        details: apiEvent.description || \"\",\n        sectionPricing: apiEvent.sectionPricing.map((sp)=>({\n                sectionId: sp.sectionId,\n                price: sp.price\n            })),\n        status: apiEvent.status,\n        createdAt: new Date(apiEvent.createdAt),\n        updatedAt: new Date(apiEvent.updatedAt)\n    };\n}\n/**\n * Convert Event model to CreateEventRequest for API\n */ function adaptEventToCreateRequest(event) {\n    // Handle both Event model and CreateEventDTO\n    let startDate;\n    let endDate;\n    if (event.startDateTime && event.endDateTime) {\n        // Event model format\n        startDate = event.startDateTime instanceof Date ? event.startDateTime : new Date(event.startDateTime);\n        endDate = event.endDateTime instanceof Date ? event.endDateTime : new Date(event.endDateTime);\n    } else if (event.startDateTime && event.endDateTime) {\n        // CreateEventDTO format (string dates)\n        startDate = new Date(event.startDateTime);\n        endDate = new Date(event.endDateTime);\n    } else {\n        throw new Error(\"Start and end date times are required\");\n    }\n    return {\n        title: event.name || event.title || \"\",\n        description: event.description,\n        venueId: event.venueId || \"\",\n        date: startDate.toISOString().split(\"T\")[0],\n        startTime: startDate.toTimeString().slice(0, 5),\n        endTime: endDate.toTimeString().slice(0, 5),\n        sectionPricing: event.sectionPricing || []\n    };\n}\n/**\n * Convert ApiVenue to Venue model\n */ function adaptApiVenueToVenue(apiVenue) {\n    var _apiVenue_sections;\n    return {\n        venueId: apiVenue.venueId,\n        name: apiVenue.name,\n        address: apiVenue.address,\n        city: apiVenue.city,\n        ownerUserId: apiVenue.ownerUserId || \"\",\n        sections: ((_apiVenue_sections = apiVenue.sections) === null || _apiVenue_sections === void 0 ? void 0 : _apiVenue_sections.map((section)=>{\n            var _section_seats;\n            return {\n                sectionId: section.sectionId,\n                venueId: section.venueId,\n                name: section.name,\n                capacity: section.capacity,\n                seats: ((_section_seats = section.seats) === null || _section_seats === void 0 ? void 0 : _section_seats.map((seat)=>({\n                        seatId: seat.seatId,\n                        sectionId: seat.sectionId,\n                        seatNumber: seat.seatNumber,\n                        rowNumber: seat.rowNumber,\n                        seatInRow: seat.seatInRow,\n                        status: \"available\"\n                    }))) || []\n            };\n        })) || [],\n        createdAt: apiVenue.createdAt,\n        updatedAt: apiVenue.updatedAt\n    };\n}\n/**\n * Store authentication token and user info\n */ function storeAuthData(authResponse) {\n    if (authResponse.token) {\n        // Store token in cookie (handled by BaseService interceptors)\n        document.cookie = \"token=\".concat(authResponse.token, \"; path=/; \").concat(authResponse.expiration ? \"expires=\".concat(new Date(authResponse.expiration).toUTCString(), \";\") : \"\");\n    }\n}\n/**\n * Clear authentication data\n */ function clearAuthData() {\n    document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n    document.cookie = \"refreshToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/apiAdapters.ts\n"));

/***/ })

});