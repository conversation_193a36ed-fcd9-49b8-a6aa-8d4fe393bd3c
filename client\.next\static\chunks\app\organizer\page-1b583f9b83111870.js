(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[688],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(5155);r(2115);var n=r(4624),i=r(2085),o=r(6707);let a=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-darkText shadow-xs hover:bg-primary/70",outline:"border bg-transparent border-darkStroke shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...c}=e,d=l?n.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(a({variant:r,size:i,className:t})),...c})}},1964:(e,t,r)=>{Promise.resolve().then(r.bind(r,8114))},2269:(e,t,r)=>{"use strict";var s=r(9509);r(8375);var n=r(2115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(n),o=void 0!==s&&s.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,n=t.optimizeForSpeed,i=void 0===n?o:n;c(a(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",c("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(c(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var s=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,s))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var s=this._tags[e];c(s,"old rule at index `"+e+"` not found"),s.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&c(a(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return r?n.insertBefore(s,r):n.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},u={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return u[s]||(u[s]="jsx-"+d(e+"-"+r)),u[s]}function p(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return u[r]||(u[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[r]}var f=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,n=t.optimizeForSpeed,i=void 0!==n&&n;this._sheet=s||new l({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),s&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),s=r.styleId,n=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var i=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=i,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var n=h(s,r);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return p(n,e)}):[p(n,t)]}}return{styleId:h(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=n.createContext(null);m.displayName="StyleSheetContext";var v=i.default.useInsertionEffect||i.default.useLayoutEffect,x="undefined"!=typeof window?new f:void 0;function y(e){var t=x||n.useContext(m);return t&&("undefined"==typeof window?t.add(e):v(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=y},6707:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(2596),n=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}},6783:(e,t,r)=>{"use strict";r.d(t,{mJ:()=>m,KC:()=>f,Qb:()=>v});var s=r(2115),n=r(2522);let i=e=>e.events.events,o=e=>e.events.currentEvent,a=e=>e.events.isLoadingList,l=e=>e.events.isLoadingDetails,c=e=>e.events.isLoadingMutation,d=e=>e.events.errorList,u=e=>e.events.errorDetails,h=e=>e.events.errorMutation;var p=r(8710);let f=()=>{let e=(0,p.j)(),t=(0,p.G)(i),r=(0,p.G)(a);return{events:t,isLoadingList:r,errorEventList:(0,p.G)(d),loadEvents:(0,s.useCallback)(()=>e((0,n.fw)()),[e])}},m=()=>{let e=(0,p.j)(),t=(0,p.G)(o),r=(0,p.G)(l),i=(0,p.G)(u);return{event:t,isLoadingEventDetails:r,errorEventDetails:i,loadEvent:(0,s.useCallback)(t=>e((0,n.vR)(t)),[e]),clearDetails:(0,s.useCallback)(()=>{e((0,n.HB)())},[e])}},v=()=>{let e=(0,p.j)(),t=(0,p.G)(c),r=(0,p.G)(h),i=(0,s.useCallback)(t=>e((0,n.lh)(t)).unwrap(),[e]),o=(0,s.useCallback)((t,r)=>e((0,n.qM)({eventId:t,eventData:r})).unwrap(),[e]),a=(0,s.useCallback)(t=>e((0,n.SX)(t)).unwrap(),[e]),l=(0,s.useCallback)((t,r)=>e((0,n.nK)({eventId:t,rescheduleData:r})).unwrap(),[e]),d=(0,s.useCallback)(t=>e((0,n.ls)(t)).unwrap(),[e]),u=(0,s.useCallback)(t=>e((0,n.TL)(t)).unwrap(),[e]),f=(0,s.useCallback)(t=>e((0,n.i$)(t)).unwrap(),[e]);return{isLoadingEventMuatation:t,errorEventMutation:r,createEvent:i,updateEvent:o,removeEvent:a,approveEvent:f,rescheduleEvent:l,postponeEvent:d,cancelEvent:u,submitEvent:(0,s.useCallback)(t=>e((0,n.P_)(t)).unwrap(),[e]),clearError:(0,s.useCallback)(()=>{e((0,n.b9)())},[e])}}},7023:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(5155);r(2115);var n=r(6707);let i=e=>{let{size:t="2.5rem",color:r="primary",thickness:i=4,className:o}=e,a={strokeWidth:i},l="stroke-blue-700";return"primary"===r?l="stroke-primary":"secondary"===r?l="stroke-gray-500":"inherit"===r&&(l="stroke-current"),(0,s.jsxs)("svg",{className:(0,n.cn)("animate-spin",o),style:{width:t,height:t},viewBox:"0 0 50 50",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("circle",{className:(0,n.cn)("opacity-25",l),cx:"25",cy:"25",r:"20",fill:"none",style:a}),(0,s.jsx)("circle",{className:(0,n.cn)("opacity-75",l),cx:"25",cy:"25",r:"20",fill:"none",strokeDasharray:"31.415, 125.66",strokeLinecap:"round",style:a})]})},o=()=>(0,s.jsx)("div",{className:"fixed inset-0 z-50 bg-slate-600 bg-opacity-50 flex items-center justify-center",children:(0,s.jsx)(i,{size:"3rem",color:"primary"})})},8114:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(5155),n=r(9137),i=r.n(n),o=r(285),a=r(7023),l=r(6783),c=r(6874),d=r.n(c),u=r(2115);let h="\n  .hide-scrollbar {\n    -ms-overflow-style: none;  /* IE and Edge */\n    scrollbar-width: none;  /* Firefox */\n  }\n  .hide-scrollbar::-webkit-scrollbar {\n    display: none;  /* Chrome, Safari and Opera */\n  }\n";function p(){let{events:e,isLoadingList:t,loadEvents:r}=(0,l.KC)();if((0,u.useEffect)(()=>{r()},[r]),t)return(0,s.jsx)(a.A,{});let n=e=>{switch(e){case"Draft":return"text-gray-700";case"Published":return"text-blue-700";case"Submit for approval":return"text-yellow-700";case"Postponed":return"text-red-700";case"Rescheduled":return"text-green-700";default:return""}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i(),{id:h.__hash,children:h}),(0,s.jsxs)("div",{style:{height:"calc(100vh - 70px)",maxHeight:"calc(100vh - 70px)"},className:"max-w-[1600px] container mx-auto px-20 py-6 flex flex-col gap-6 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-darkText",children:"Hi organizer! Your events are here"}),(0,s.jsx)("p",{className:"text-grayText",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."})]}),(0,s.jsx)(d(),{href:"/organizer/event/new",children:(0,s.jsx)(o.$,{className:"bg-secondary hover:bg-secondary/80 text-whiteText px-5 font-bold py-2 rounded-md",children:"Create event"})})]}),(0,s.jsxs)("div",{style:{boxShadow:"0px 4px 4px 0px rgba(0, 0, 0, 0.25)"},className:"border border-darkStroke rounded-lg flex-grow flex flex-col overflow-hidden",children:[(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)("table",{className:"w-full table-fixed",children:(0,s.jsx)("thead",{className:"bg-secondary text-whiteText",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[3%]",children:"#"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[25%]",children:"Name"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[15%]",children:"Date"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[10%]",children:"Sold"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[10%]",children:"Available"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[10%]",children:"Revenue"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[10%]",children:"Status"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[7%]",children:"Action"})]})})})}),(0,s.jsx)("div",{className:"overflow-y-auto flex-grow hide-scrollbar",children:(0,s.jsx)("table",{className:"w-full table-fixed",children:(0,s.jsx)("tbody",{className:"bg-white divide-y divide-darkStroke",children:e.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"w-[3%] py-3 text-center whitespace-nowrap text-sm text-darkText",children:t+1}),(0,s.jsx)("td",{className:"w-[25%] py-3 text-center whitespace-nowrap text-sm font-medium text-darkText",children:e.name}),(0,s.jsx)("td",{className:"w-[15%] py-3 text-center whitespace-nowrap text-sm text-darkText",children:e.startDateTime.toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0})}),(0,s.jsx)("td",{className:"w-[10%] py-3 text-center whitespace-nowrap text-sm text-darkText"}),(0,s.jsx)("td",{className:"w-[10%] py-3 text-center whitespace-nowrap text-sm text-darkText"}),(0,s.jsx)("td",{className:"w-[10%] py-3 text-center whitespace-nowrap text-sm text-darkText",children:"$"}),(0,s.jsx)("td",{className:"w-[10%] py-3 text-center whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm ".concat(n(e.status)),children:e.status})}),(0,s.jsx)("td",{className:"w-[7%] py-3 text-center whitespace-nowrap text-sm text-darkText",children:(0,s.jsx)(d(),{href:"/organizer/event/".concat(e.eventId),children:(0,s.jsx)("button",{className:"text-secondary hover:text-secondary/70",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"22",height:"22",viewBox:"0 0 22 22",fill:"none",children:(0,s.jsx)("path",{d:"M9.9551 3.17518H3.0561C2.53332 3.17518 2.03196 3.38286 1.66229 3.75252C1.29263 4.12218 1.08496 4.62355 1.08496 5.14633V18.9443C1.08496 19.4671 1.29263 19.9685 1.66229 20.3381C2.03196 20.7078 2.53332 20.9155 3.0561 20.9155H16.8541C17.3769 20.9155 17.8782 20.7078 18.2479 20.3381C18.6176 19.9685 18.8252 19.4671 18.8252 18.9443V12.0453M17.3469 1.69683C17.739 1.30474 18.2707 1.08447 18.8252 1.08447C19.3797 1.08447 19.9115 1.30474 20.3036 1.69683C20.6957 2.08891 20.9159 2.62069 20.9159 3.17518C20.9159 3.72968 20.6957 4.26146 20.3036 4.65354L10.9407 14.0165L6.99839 15.002L7.98396 11.0598L17.3469 1.69683Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})})})]},e.eventId))})})})]})]})]})}},8375:()=>{},8710:(e,t,r)=>{"use strict";r.d(t,{G:()=>i,j:()=>n});var s=r(4540);let n=s.wA.withTypes(),i=s.d4.withTypes()},9137:(e,t,r)=>{"use strict";e.exports=r(2269).style}},e=>{var t=t=>e(e.s=t);e.O(0,[13,874,196,522,441,684,358],()=>t(1964)),_N_E=e.O()}]);