(()=>{var e={};e.id=688,e.ids=[688],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(60687);r(43210);var n=r(81391),i=r(24224),a=r(76279);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-darkText shadow-xs hover:bg-primary/70",outline:"border bg-transparent border-darkStroke shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:i=!1,...l}){let c=i?n.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,a.cn)(o({variant:t,size:r,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},35920:(e,t,r)=>{"use strict";r.d(t,{mJ:()=>m,KC:()=>x,Qb:()=>f});var s=r(43210),n=r(3377);let i=e=>e.events.events,a=e=>e.events.currentEvent,o=e=>e.events.isLoadingList,l=e=>e.events.isLoadingDetails,c=e=>e.events.isLoadingMutation,d=e=>e.events.errorList,u=e=>e.events.errorDetails,h=e=>e.events.errorMutation;var p=r(85908);let x=()=>{let e=(0,p.j)(),t=(0,p.G)(i),r=(0,p.G)(o);return{events:t,isLoadingList:r,errorEventList:(0,p.G)(d),loadEvents:(0,s.useCallback)(()=>e((0,n.fw)()),[e])}},m=()=>{let e=(0,p.j)(),t=(0,p.G)(a),r=(0,p.G)(l),i=(0,p.G)(u);return{event:t,isLoadingEventDetails:r,errorEventDetails:i,loadEvent:(0,s.useCallback)(t=>e((0,n.vR)(t)),[e]),clearDetails:(0,s.useCallback)(()=>{e((0,n.HB)())},[e])}},f=()=>{let e=(0,p.j)(),t=(0,p.G)(c),r=(0,p.G)(h),i=(0,s.useCallback)(t=>e((0,n.lh)(t)).unwrap(),[e]),a=(0,s.useCallback)((t,r)=>e((0,n.qM)({eventId:t,eventData:r})).unwrap(),[e]),o=(0,s.useCallback)(t=>e((0,n.SX)(t)).unwrap(),[e]),l=(0,s.useCallback)((t,r)=>e((0,n.nK)({eventId:t,rescheduleData:r})).unwrap(),[e]),d=(0,s.useCallback)(t=>e((0,n.ls)(t)).unwrap(),[e]),u=(0,s.useCallback)(t=>e((0,n.TL)(t)).unwrap(),[e]),x=(0,s.useCallback)(t=>e((0,n.i$)(t)).unwrap(),[e]);return{isLoadingEventMuatation:t,errorEventMutation:r,createEvent:i,updateEvent:a,removeEvent:o,approveEvent:x,rescheduleEvent:l,postponeEvent:d,cancelEvent:u,submitEvent:(0,s.useCallback)(t=>e((0,n.P_)(t)).unwrap(),[e]),clearError:(0,s.useCallback)(()=>{e((0,n.b9)())},[e])}}},50142:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Study\\\\Classes\\\\6thSemester\\\\SoftwareArchitecture\\\\project\\\\client\\\\src\\\\app\\\\organizer\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\page.tsx","default")},50516:(e,t,r)=>{Promise.resolve().then(r.bind(r,50142))},52027:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(60687);r(43210);var n=r(76279);let i=({size:e="2.5rem",color:t="primary",thickness:r=4,className:i})=>{let a={strokeWidth:r},o="stroke-blue-700";return"primary"===t?o="stroke-primary":"secondary"===t?o="stroke-gray-500":"inherit"===t&&(o="stroke-current"),(0,s.jsxs)("svg",{className:(0,n.cn)("animate-spin",i),style:{width:e,height:e},viewBox:"0 0 50 50",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("circle",{className:(0,n.cn)("opacity-25",o),cx:"25",cy:"25",r:"20",fill:"none",style:a}),(0,s.jsx)("circle",{className:(0,n.cn)("opacity-75",o),cx:"25",cy:"25",r:"20",fill:"none",strokeDasharray:"31.415, 125.66",strokeLinecap:"round",style:a})]})},a=()=>(0,s.jsx)("div",{className:"fixed inset-0 z-50 bg-slate-600 bg-opacity-50 flex items-center justify-center",children:(0,s.jsx)(i,{size:"3rem",color:"primary"})})},52724:(e,t,r)=>{Promise.resolve().then(r.bind(r,68540))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68540:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),n=r(76180),i=r.n(n),a=r(29523),o=r(52027),l=r(35920),c=r(85814),d=r.n(c);r(43210);let u=`
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .hide-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
`;function h(){let{events:e,isLoadingList:t,loadEvents:r}=(0,l.KC)();if(t)return(0,s.jsx)(o.A,{});let n=e=>{switch(e){case"Draft":return"text-gray-700";case"Published":return"text-blue-700";case"Submit for approval":return"text-yellow-700";case"Postponed":return"text-red-700";case"Rescheduled":return"text-green-700";default:return""}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i(),{id:u.__hash,children:u}),(0,s.jsxs)("div",{style:{height:"calc(100vh - 70px)",maxHeight:"calc(100vh - 70px)"},className:"max-w-[1600px] container mx-auto px-20 py-6 flex flex-col gap-6 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-darkText",children:"Hi organizer! Your events are here"}),(0,s.jsx)("p",{className:"text-grayText",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."})]}),(0,s.jsx)(d(),{href:"/organizer/event/new",children:(0,s.jsx)(a.$,{className:"bg-secondary hover:bg-secondary/80 text-whiteText px-5 font-bold py-2 rounded-md",children:"Create event"})})]}),(0,s.jsxs)("div",{style:{boxShadow:"0px 4px 4px 0px rgba(0, 0, 0, 0.25)"},className:"border border-darkStroke rounded-lg flex-grow flex flex-col overflow-hidden",children:[(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)("table",{className:"w-full table-fixed",children:(0,s.jsx)("thead",{className:"bg-secondary text-whiteText",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[3%]",children:"#"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[25%]",children:"Name"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[15%]",children:"Date"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[10%]",children:"Sold"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[10%]",children:"Available"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[10%]",children:"Revenue"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[10%]",children:"Status"}),(0,s.jsx)("th",{scope:"col",className:"py-4 text-center font-bold w-[7%]",children:"Action"})]})})})}),(0,s.jsx)("div",{className:"overflow-y-auto flex-grow hide-scrollbar",children:(0,s.jsx)("table",{className:"w-full table-fixed",children:(0,s.jsx)("tbody",{className:"bg-white divide-y divide-darkStroke",children:e.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"w-[3%] py-3 text-center whitespace-nowrap text-sm text-darkText",children:t+1}),(0,s.jsx)("td",{className:"w-[25%] py-3 text-center whitespace-nowrap text-sm font-medium text-darkText",children:e.name}),(0,s.jsx)("td",{className:"w-[15%] py-3 text-center whitespace-nowrap text-sm text-darkText",children:e.startDateTime.toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0})}),(0,s.jsx)("td",{className:"w-[10%] py-3 text-center whitespace-nowrap text-sm text-darkText"}),(0,s.jsx)("td",{className:"w-[10%] py-3 text-center whitespace-nowrap text-sm text-darkText"}),(0,s.jsx)("td",{className:"w-[10%] py-3 text-center whitespace-nowrap text-sm text-darkText",children:"$"}),(0,s.jsx)("td",{className:"w-[10%] py-3 text-center whitespace-nowrap",children:(0,s.jsx)("span",{className:`text-sm ${n(e.status)}`,children:e.status})}),(0,s.jsx)("td",{className:"w-[7%] py-3 text-center whitespace-nowrap text-sm text-darkText",children:(0,s.jsx)(d(),{href:`/organizer/event/${e.eventId}`,children:(0,s.jsx)("button",{className:"text-secondary hover:text-secondary/70",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"22",height:"22",viewBox:"0 0 22 22",fill:"none",children:(0,s.jsx)("path",{d:"M9.9551 3.17518H3.0561C2.53332 3.17518 2.03196 3.38286 1.66229 3.75252C1.29263 4.12218 1.08496 4.62355 1.08496 5.14633V18.9443C1.08496 19.4671 1.29263 19.9685 1.66229 20.3381C2.03196 20.7078 2.53332 20.9155 3.0561 20.9155H16.8541C17.3769 20.9155 17.8782 20.7078 18.2479 20.3381C18.6176 19.9685 18.8252 19.4671 18.8252 18.9443V12.0453M17.3469 1.69683C17.739 1.30474 18.2707 1.08447 18.8252 1.08447C19.3797 1.08447 19.9115 1.30474 20.3036 1.69683C20.6957 2.08891 20.9159 2.62069 20.9159 3.17518C20.9159 3.72968 20.6957 4.26146 20.3036 4.65354L10.9407 14.0165L6.99839 15.002L7.98396 11.0598L17.3469 1.69683Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})})})]},e.eventId))})})})]})]})]})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,r)=>{"use strict";r(56397);var s=r(43210),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),i="undefined"!=typeof process&&process.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,n=t.optimizeForSpeed,o=void 0===n?i:n;l(a(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",l("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return l(a(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return r?n.insertBefore(s,r):n.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function u(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return d[s]||(d[s]="jsx-"+c(e+"-"+r)),d[s]}function h(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,n=t.optimizeForSpeed,i=void 0!==n&&n;this._sheet=s||new o({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),s&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,n=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var i=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=i,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var n=u(s,r);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return h(n,e)}):[h(n,t)]}}return{styleId:u(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),x=s.createContext(null);x.displayName="StyleSheetContext";n.default.useInsertionEffect||n.default.useLayoutEffect;var m=void 0;function f(e){var t=m||s.useContext(x);return t&&t.add(e),null}f.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=f},76180:(e,t,r)=>{"use strict";e.exports=r(75913).style},76279:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(49384),n=r(82348);function i(...e){return(0,n.QP)((0,s.$)(e))}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83929:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["organizer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,50142)),"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,53039)),"D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Study\\Classes\\6thSemester\\SoftwareArchitecture\\project\\client\\src\\app\\organizer\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/organizer/page",pathname:"/organizer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83997:e=>{"use strict";e.exports=require("tty")},85908:(e,t,r)=>{"use strict";r.d(t,{G:()=>i,j:()=>n});var s=r(54864);let n=s.wA.withTypes(),i=s.d4.withTypes()},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,653,658,23,695],()=>r(83929));module.exports=s})();