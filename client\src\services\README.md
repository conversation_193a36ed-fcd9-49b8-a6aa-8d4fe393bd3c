# Services Architecture Documentation

## Overview

This document describes the refactored service architecture using a centralized `BaseService` class that provides consistent API communication, authentication, error handling, and retry logic across all services. All services have been updated to work with the backend microservices API Gateway.

## API Gateway Integration

All services now communicate through the API Gateway at:
- **Development**: `http://localhost:8080`
- **Production**: `https://api.ticketselling.thyqtran.id.vn`

## Service Implementations

### 1. AuthService (userService.ts)
- **Endpoints**: `/api/auth/*`
- **Authentication**: Disabled (for login/signup)
- **Methods**: 
  - `login(credentials)` → Returns `AuthResponse` with JWT token
  - `signup(userData)` → Returns success message and userId
  - `forgotPassword(email)` → Returns success message

### 2. UserService (userService.ts)
- **Endpoints**: `/api/user/*`
- **Authentication**: Enabled
- **Methods**: 
  - `getAllUsers()` → Admin only
  - `getUserById(id)` → Get user profile
  - `updateUser(id, data)` → Update user profile
  - `deleteUser(id)` → Delete user account

### 3. EventService (eventService.ts)
- **Endpoints**: `/api/events/*`
- **Authentication**: Enabled
- **Methods**: 
  - `getAllEvents()` → Get all events
  - `getPublishedEvents()` → Get only published events
  - `createEvent(data)` → Create new event (Organizer/Admin)
  - `updateEvent(id, data)` → Update event (Owner/Admin)
  - `deleteEvent(id)` → Delete event (Owner/Admin)
  - `submitEvent(id)` → Submit for approval
  - `approveEvent(id)` → Approve event (Admin only)
  - `cancelEvent(id)` → Cancel event
  - `postponeEvent(id)` → Postpone event
  - `rescheduleEvent(id, data)` → Reschedule event

### 4. VenueService (venueService.ts)
- **Endpoints**: `/api/venues/*`
- **Authentication**: Enabled
- **Methods**: 
  - `getAllVenues()` → Get all venues
  - `getVenueById(id)` → Get venue details
  - `createVenue(data)` → Create venue (Organizer/Admin)
  - `updateVenue(id, data)` → Update venue (Owner/Admin)
  - `deleteVenue(id)` → Delete venue (Owner/Admin)
  - `getAllSeatsForVenue(venueId)` → Get all seats
  - `getAllSectionsForVenue(venueId)` → Get sections
  - `createSection(venueId, data)` → Create section
  - `updateSection(venueId, sectionId, data)` → Update section
  - `deleteSection(venueId, sectionId)` → Delete section

### 5. BookingService (bookingService.ts) - NEW
- **Endpoints**: `/api/bookings/*`
- **Authentication**: Enabled
- **Methods**: 
  - `createBooking(data)` → Create new booking
  - `getMyBookings()` → Get user's bookings
  - `getSeatStatusForEvent(eventId)` → Get seat availability

### 6. PaymentService (paymentService.ts) - NEW
- **Endpoints**: `/api/payments/*`
- **Authentication**: Enabled
- **Methods**: 
  - `initiatePayment(data)` → Start payment process
  - `getPaymentStatus(paymentId)` → Check payment status

## Key Features

### ✅ Backend API Compliance
- All endpoints match the microservices API documentation
- Proper request/response type mapping
- Correct HTTP methods and status codes
- Authentication headers automatically handled

### ✅ Type Safety
- Full TypeScript support with backend API types
- Request/response adapters for model conversion
- Proper error type handling

### ✅ Error Handling
- Backend error response format support
- Validation error handling with field-specific messages
- Proper HTTP status code handling (401, 403, 404, 409, etc.)

### ✅ Authentication
- JWT token management through cookies
- Automatic token refresh
- Role-based access control support

## Data Adapters

The `apiAdapters.ts` utility provides conversion between:
- Backend API responses ↔ Frontend models
- Authentication token management
- Date/time format conversion
- Model structure mapping

## Migration Benefits

1. **API Compliance**: All services now work with the actual backend API
2. **Type Safety**: Full TypeScript coverage with proper API types
3. **Error Handling**: Consistent error handling across all services
4. **Authentication**: Centralized JWT token management
5. **Maintainability**: Single source of truth for API communication
6. **Scalability**: Easy to add new services following the same pattern

## Usage Examples

```typescript
// Authentication
import authService from '@/services/userService';
const authResponse = await authService.login({ email, password });

// Event Management
import eventService from '@/services/eventService';
const events = await eventService.getPublishedEvents();

// Booking
import bookingService from '@/services/bookingService';
const booking = await bookingService.createBooking({
  eventId: "event-id",
  seatIds: ["seat1", "seat2"]
});

// Payment
import paymentService from '@/services/paymentService';
const payment = await paymentService.initiatePayment({
  bookingId: "booking-id",
  amount: 100.00,
  currency: "VND",
  gateway: "VnPay",
  returnUrl: "https://app.com/success",
  cancelUrl: "https://app.com/cancel"
});
```

All existing components continue to work without changes thanks to the adapter pattern and maintained public interfaces.
