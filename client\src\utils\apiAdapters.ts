// API Response Adapters
// Convert backend API responses to frontend models

import { User } from "@/models/User";
import { AuthResponse, ApiUser, ApiEvent, ApiVenue } from "@/types/api";
import Event, { EventCategory, EventStatus, EventSectionPricing } from "@/models/Event";
import { Venue } from "@/models/Venue";
import { UserRole } from "@/constants";

/**
 * Convert AuthResponse to User model for Redux state
 * Note: AuthResponse doesn't contain full user data, so we create a minimal User object
 */
export function adaptAuthResponseToUser(authResponse: AuthResponse): User {
  return {
    userId: authResponse.userId || "",
    userName: "", // Not provided in auth response
    email: "", // Not provided in auth response
    firstName: "", // Not provided in auth response
    lastName: "", // Not provided in auth response
    role: UserRole.USER, // Default role
  };
}

/**
 * Convert ApiUser to User model
 */
export function adaptApiUserToUser(apiUser: ApiUser): User {
  return {
    userId: apiUser.id,
    userName: apiUser.username,
    email: apiUser.email,
    firstName: apiUser.firstName,
    lastName: apiUser.lastName,
    role: UserRole.USER, // Default role, should be mapped from backend
  };
}

/**
 * Convert ApiEvent to Event model with proper date parsing
 */
export function adaptApiEventToEvent(apiEvent: ApiEvent): Event {
  // Combine date and time strings to create full DateTime
  const startDateTime = new Date(`${apiEvent.date}T${apiEvent.startTime}`);
  const endDateTime = new Date(`${apiEvent.date}T${apiEvent.endTime}`);

  // Map backend status to frontend enum
  const mapStatus = (backendStatus: string): EventStatus => {
    switch (backendStatus) {
      case "Draft":
        return EventStatus.DRAFT;
      case "Pending":
        return EventStatus.SUBMIT_FOR_APPROVAL;
      case "Approved":
        return EventStatus.PUBLISHED;
      case "Cancelled":
        return EventStatus.CANCELED;
      case "Postponed":
        return EventStatus.POSTPONED;
      case "Rescheduled":
        return EventStatus.RESCHEDULED;
      default:
        return EventStatus.DRAFT;
    }
  };

  return {
    eventId: apiEvent.eventId,
    name: apiEvent.title,
    description: apiEvent.description || "",
    category: EventCategory.CONCERT, // Default category, should be mapped from backend
    startDateTime,
    endDateTime,
    venueId: apiEvent.venueId,
    venueName: "", // Not provided in API response
    venueAddress: "", // Not provided in API response
    poster: "", // Not provided in API response
    organizerUserId: apiEvent.organizerId,
    images: [], // Not provided in API response
    details: apiEvent.description || "",
    sectionPricing: apiEvent.sectionPricing.map(
      (sp): EventSectionPricing => ({
        eventId: apiEvent.eventId,
        sectionId: sp.sectionId,
        price: sp.price,
      })
    ),
    status: mapStatus(apiEvent.status),
    createdAt: new Date(apiEvent.createdAt),
    updatedAt: new Date(apiEvent.updatedAt),
  };
}

/**
 * Convert Event model to CreateEventRequest for API
 */
export function adaptEventToCreateRequest(event: Partial<Event> | any) {
  // Handle both Event model and CreateEventDTO
  let startDate: Date;
  let endDate: Date;

  if (event.startDateTime && event.endDateTime) {
    // Event model format
    startDate =
      event.startDateTime instanceof Date ? event.startDateTime : new Date(event.startDateTime);
    endDate = event.endDateTime instanceof Date ? event.endDateTime : new Date(event.endDateTime);
  } else if (event.startDateTime && event.endDateTime) {
    // CreateEventDTO format (string dates)
    startDate = new Date(event.startDateTime);
    endDate = new Date(event.endDateTime);
  } else {
    throw new Error("Start and end date times are required");
  }

  return {
    title: event.name || event.title || "",
    description: event.description,
    venueId: event.venueId || "",
    date: startDate.toISOString().split("T")[0], // YYYY-MM-DD format
    startTime: startDate.toTimeString().slice(0, 5), // HH:mm format
    endTime: endDate.toTimeString().slice(0, 5), // HH:mm format
    sectionPricing: event.sectionPricing || [],
  };
}

/**
 * Convert ApiVenue to Venue model
 */
export function adaptApiVenueToVenue(apiVenue: ApiVenue): Venue {
  return {
    venueId: apiVenue.venueId,
    name: apiVenue.name,
    address: apiVenue.address,
    city: apiVenue.city,
    ownerUserId: apiVenue.ownerUserId || "",
    sections:
      apiVenue.sections?.map(section => ({
        sectionId: section.sectionId,
        venueId: section.venueId,
        name: section.name,
        capacity: section.capacity,
        seats:
          section.seats?.map(seat => ({
            seatId: seat.seatId,
            sectionId: seat.sectionId,
            seatNumber: seat.seatNumber,
            rowNumber: seat.rowNumber,
            seatInRow: seat.seatInRow,
            status: "available" as const,
          })) || [],
      })) || [],
    createdAt: apiVenue.createdAt,
    updatedAt: apiVenue.updatedAt,
  };
}

/**
 * Store authentication token and user info
 */
export function storeAuthData(authResponse: AuthResponse): void {
  if (authResponse.token) {
    // Store token in cookie (handled by BaseService interceptors)
    document.cookie = `token=${authResponse.token}; path=/; ${authResponse.expiration ? `expires=${new Date(authResponse.expiration).toUTCString()};` : ""}`;
  }
}

/**
 * Clear authentication data
 */
export function clearAuthData(): void {
  document.cookie = "token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
  document.cookie = "refreshToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
}
