"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/[slug]/page",{

/***/ "(app-pages-browser)/./src/utils/apiAdapters.ts":
/*!**********************************!*\
  !*** ./src/utils/apiAdapters.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adaptApiEventToEvent: () => (/* binding */ adaptApiEventToEvent),\n/* harmony export */   adaptApiUserToUser: () => (/* binding */ adaptApiUserToUser),\n/* harmony export */   adaptApiVenueToVenue: () => (/* binding */ adaptApiVenueToVenue),\n/* harmony export */   adaptAuthResponseToUser: () => (/* binding */ adaptAuthResponseToUser),\n/* harmony export */   adaptEventToCreateRequest: () => (/* binding */ adaptEventToCreateRequest),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   storeAuthData: () => (/* binding */ storeAuthData)\n/* harmony export */ });\n// API Response Adapters\n// Convert backend API responses to frontend models\n/**\n * Convert AuthResponse to User model for Redux state\n * Note: AuthResponse doesn't contain full user data, so we create a minimal User object\n */ function adaptAuthResponseToUser(authResponse) {\n    return {\n        userId: authResponse.userId,\n        userName: \"\",\n        email: \"\",\n        password: \"\",\n        firstName: \"\",\n        lastName: \"\",\n        createdAt: new Date(),\n        updatedAt: new Date()\n    };\n}\n/**\n * Convert ApiUser to User model\n */ function adaptApiUserToUser(apiUser) {\n    return {\n        userId: apiUser.id,\n        userName: apiUser.username,\n        email: apiUser.email,\n        password: \"\",\n        firstName: apiUser.firstName,\n        lastName: apiUser.lastName,\n        createdAt: new Date(apiUser.createdAt),\n        updatedAt: new Date()\n    };\n}\n/**\n * Convert ApiEvent to Event model with proper date parsing\n */ function adaptApiEventToEvent(apiEvent) {\n    // Combine date and time strings to create full DateTime\n    const startDateTime = new Date(\"\".concat(apiEvent.date, \"T\").concat(apiEvent.startTime));\n    const endDateTime = new Date(\"\".concat(apiEvent.date, \"T\").concat(apiEvent.endTime));\n    return {\n        eventId: apiEvent.eventId,\n        name: apiEvent.title,\n        description: apiEvent.description || \"\",\n        category: \"Concert\",\n        startDateTime,\n        endDateTime,\n        venueId: apiEvent.venueId,\n        venueName: \"\",\n        venueAddress: \"\",\n        poster: \"\",\n        organizerUserId: apiEvent.organizerId,\n        images: [],\n        details: apiEvent.description || \"\",\n        sectionPricing: apiEvent.sectionPricing.map((sp)=>({\n                sectionId: sp.sectionId,\n                price: sp.price\n            })),\n        status: apiEvent.status,\n        createdAt: new Date(apiEvent.createdAt),\n        updatedAt: new Date(apiEvent.updatedAt)\n    };\n}\n/**\n * Convert Event model to CreateEventRequest for API\n */ function adaptEventToCreateRequest(event) {\n    // Handle both Event model and CreateEventDTO\n    let startDate;\n    let endDate;\n    if (event.startDateTime && event.endDateTime) {\n        // Event model format\n        startDate = event.startDateTime instanceof Date ? event.startDateTime : new Date(event.startDateTime);\n        endDate = event.endDateTime instanceof Date ? event.endDateTime : new Date(event.endDateTime);\n    } else if (event.startDateTime && event.endDateTime) {\n        // CreateEventDTO format (string dates)\n        startDate = new Date(event.startDateTime);\n        endDate = new Date(event.endDateTime);\n    } else {\n        throw new Error(\"Start and end date times are required\");\n    }\n    return {\n        title: event.name || event.title || \"\",\n        description: event.description,\n        venueId: event.venueId || \"\",\n        date: startDate.toISOString().split(\"T\")[0],\n        startTime: startDate.toTimeString().slice(0, 5),\n        endTime: endDate.toTimeString().slice(0, 5),\n        sectionPricing: event.sectionPricing || []\n    };\n}\n/**\n * Convert ApiVenue to Venue model\n */ function adaptApiVenueToVenue(apiVenue) {\n    var _apiVenue_sections;\n    return {\n        venueId: apiVenue.venueId,\n        name: apiVenue.name,\n        address: apiVenue.address,\n        city: apiVenue.city,\n        ownerUserId: apiVenue.ownerUserId || \"\",\n        sections: ((_apiVenue_sections = apiVenue.sections) === null || _apiVenue_sections === void 0 ? void 0 : _apiVenue_sections.map((section)=>{\n            var _section_seats;\n            return {\n                sectionId: section.sectionId,\n                venueId: section.venueId,\n                name: section.name,\n                capacity: section.capacity,\n                seats: ((_section_seats = section.seats) === null || _section_seats === void 0 ? void 0 : _section_seats.map((seat)=>({\n                        seatId: seat.seatId,\n                        sectionId: seat.sectionId,\n                        seatNumber: seat.seatNumber,\n                        rowNumber: seat.rowNumber,\n                        seatInRow: seat.seatInRow,\n                        status: \"available\"\n                    }))) || []\n            };\n        })) || [],\n        createdAt: apiVenue.createdAt,\n        updatedAt: apiVenue.updatedAt\n    };\n}\n/**\n * Store authentication token and user info\n */ function storeAuthData(authResponse) {\n    if (authResponse.token) {\n        // Store token in cookie (handled by BaseService interceptors)\n        document.cookie = \"token=\".concat(authResponse.token, \"; path=/; \").concat(authResponse.expiration ? \"expires=\".concat(new Date(authResponse.expiration).toUTCString(), \";\") : \"\");\n    }\n}\n/**\n * Clear authentication data\n */ function clearAuthData() {\n    document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n    document.cookie = \"refreshToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/apiAdapters.ts\n"));

/***/ })

});